// TarotCard 타입 정의 (중복 방지를 위해 여기서 정의)
export interface TarotCard {
  id: string;
  name: string; // 한국어 이름
  imageName: string; // 이미지 파일 이름 (예: '00-TheFool.jpg')
  description: string; // 간략한 한국어 설명 (플레이스홀더)
  suit?: 'major' | 'wands' | 'cups' | 'swords' | 'pentacles';
  number?: string; // 에이스, 킹, 퀸, 나이트, 또는 숫자
}

// Updated FortuneType to include specific custom tarot types
export type FortuneType = 
  | 'today' 
  | 'year' 
  | 'custom' // 통합된 사용자 정의 타로 타입
  | 'customTarotReading' // 이전 버전 호환 또는 특정 로직용 (검토 후 정리 필요)
  | 'customThreeCard' 
  | 'customFourCard'
  | 'customFiveCard' 
  | 'customSevenCard' 
  | 'customTenCard' 
  | 'customDoubleLine'
  | 'customCelticCross'
  | 'customCrossSpread'
  | 'customPyramid'
  | 'customHorseshoe'
  | 'customAwen'
  | 'customDragonRaziel'
  | 'customBinaryChoice'
  | 'customRelationship'
  | 'customCupOfRelationship'
  | 'customYinYang'
  | 'customReadingMind'
  | 'customHoroscope'
  | null;

// New View states for the immersive experience
export type View = 
  | 'concernInput'
  | 'typeSelection' 
  | 'nameInput'
  | 'meditationIntro'
  | 'cardCountSelection' // New: User selects number of cards for custom tarot
  | 'cardDrawing'
  | 'cardRevealAndInterpretation' // New: Reveal selected cards one by one with individual AI interpretation
  | 'finalInterpretation' // New: AI gives overall reading based on 5 cards
  | 'resultEnd' // New: Options to restart, share, etc.
  | 'cooldownMessage' 
  | 'loginRequiredMessage' 
  | 'creditsRequiredMessage';

// Chat message structure
export interface ChatMessage {
  id: string; // Unique ID for each message
  sender: 'ai' | 'system' | 'user'; // System for instructions, AI for card readings
  text: string;
  card?: TarotCard; // Optional: link message to a specific card
  timestamp: number;
}

export interface ManagerSpreadPosition {
  id: string;
  left: string;
  top: string;
  transform?: string;
}

export type FiveCardSpreadType = 'W' | 'Cross' | null; // 5카드 스프레드 레이아웃 타입

export interface SpreadPosition {
  top: string;
  left: string;
  transform?: string;
}

// 확장된 타로 스프레드 인터페이스 (서버에서 받는 할인 정보 포함)
export interface ExtendedTarotSpread {
  id: string;
  name: string;
  cardCount: number;
  cost: number;
  discount: number;
  finalCost: number;
  isDiscountActive: boolean;
  discountTimeRemaining?: string;
  discountStartDate?: string;
  discountEndDate?: string;
  spreadType: string;
  className: string;
  positions?: ManagerSpreadPosition[];
}
