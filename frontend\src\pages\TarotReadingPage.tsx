import React, { useState, useEffect, useRef } from 'react';
import './TarotReadingPage.css';
import { useAuth } from '../contexts/AuthContext';
import { tarotCardsData } from '../data/tarotCardsData';
import { tarotSpreads, type TarotSpread, SPREAD_CLASS_NAMES } from '../data/tarotSpreadsData';
import TypingText from '../components/TypingText';
import {
  SOUND_PATHS,
  YEAR_FORTUNE_COST,
  NUM_CARDS_FIVE_SPREAD,
  NUM_CARDS_THREE_SPREAD,
  CUSTOM_TAROT_COST_3_CARDS
} from '../constants/tarotConstants';
import type {
  FortuneType,
  View,
  ChatMessage,
  ManagerSpreadPosition,
  TarotCard
} from '../types/tarotTypes';
import { sceneTransitionStyles } from '../styles/tarotStyles';
import { tarotCardStyles } from '../styles/tarotCardStyles';
import { getRandomCards, getSpreadLayoutStyles } from '../utils/tarotUtils';
import { useTarotSound } from '../hooks/useTarotSound';

// 스타일 주입 - 공통 스타일 모듈에서 가져옴

// 로컬 타입 정의 (필요한 경우만)
type FiveCardSpreadType = 'W' | 'Cross' | null; // 5카드 스프레드 레이아웃 타입

interface SpreadPosition {
  top: string;
  left: string;
  transform?: string;
}

// 공통 모듈에서 가져온 함수들을 사용

// 로컬에서만 필요한 상수들 (모든 상수는 constants 모듈에서 가져옴)

// 공통 스타일 모듈에서 가져온 스타일들을 사용

const TarotReadingPage: React.FC = () => {
  const [selectedFortuneType, setSelectedFortuneType] = useState<FortuneType>(null);
  const [userName, setUserName] = useState<string>('');
  const [currentView, setCurrentView] = useState<View>('concernInput'); // Default to concernInput
  const [userConcern, setUserConcern] = useState<string>(''); // State for user's concern
  const [selectedCardCountForCustom, setSelectedCardCountForCustom] = useState<number | null>(null); // New state for selected card count
  const [currentNumCardsToSelect, setCurrentNumCardsToSelect] = useState<number>(NUM_CARDS_THREE_SPREAD); // State for how many cards to actually select for the current fortune
  const [currentFortuneCost, setCurrentFortuneCost] = useState<number>(0); // State for the cost of the current custom fortune
  
  // States for the new 5-card spread flow
  const [cardsForSelection, setCardsForSelection] = useState<TarotCard[]>([]); // Cards fanned out for user to pick from
  const [selectedCards, setSelectedCards] = useState<TarotCard[]>([]); // The 5 cards chosen by the user
  const [revealedCardsInfo, setRevealedCardsInfo] = useState<Array<{card: TarotCard, interpretation: string | null}>>([]);
  const [currentCardSelectionStep, setCurrentCardSelectionStep] = useState(0); // 0 to 4 for 5 cards
  const [currentCardRevealStep, setCurrentCardRevealStep] = useState(0); // 0 to 4 for 5 cards
  const [finalInterpretationText, setFinalInterpretationText] = useState<string | null>(null);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);

  // Dynamically determine the number of cards needed based on the selected fortune type
  // const numCardsToSelect = selectedFortuneType === 'year' ? NUM_CARDS_FIVE_SPREAD : NUM_CARDS_THREE_SPREAD; // Default to 3 for today and custom
  // This will now be controlled by currentNumCardsToSelect

  // Added state for transition animation
  const [isEnteringSpace, setIsEnteringSpace] = useState<boolean>(false);

  // Existing states
  const [cooldownMessageDetail, setCooldownMessageDetail] = useState<string>('');
  const [fortuneInterpretation, setFortuneInterpretation] = useState<string | null>(null); // May be deprecated or used for final summary
  const [isLoadingFortune, setIsLoadingFortune] = useState<boolean>(false); // Will be used for AI calls
  const [apiError, setApiError] = useState<string | null>(null);

  // 공통 사운드 훅 사용
  const {
    isSoundMuted,
    setIsSoundMuted,
    soundVolume,
    setSoundVolume,
    isFortuneMusic,
    playSound,
    stopSound
  } = useTarotSound();

  // State to control button visibility in meditation intro
  const [showMeditationButton, setShowMeditationButton] = useState<boolean>(false);
  const [meditationTextIndex, setMeditationTextIndex] = useState(0);
  const [meditationTexts, setMeditationTexts] = useState<string[]>([]);
  const [showCardSelectionButton, setShowCardSelectionButton] = useState(false);

  // 새로운 상태 추가
  const [sceneTransition, setSceneTransition] = useState(false); // 장면 전환 애니메이션 상태
  const [autoRevealActive, setAutoRevealActive] = useState(false); // 자동 카드 공개 상태
  const [currentRevealingCard, setCurrentRevealingCard] = useState<number | null>(null); // 현재 공개 중인 카드 인덱스
  const [cardFlipStates, setCardFlipStates] = useState<boolean[]>([]); // 카드 뒤집기 상태 배열
  const [showTransitionOverlay, setShowTransitionOverlay] = useState(false); // 트랜지션 오버레이 상태
  const [contentOpacity, setContentOpacity] = useState(1); // 컨텐츠 불투명도 상태

  // 개별 카드 소개 관련 상태
  const [isIntroducingCard, setIsIntroducingCard] = useState<boolean>(false);
  const [currentCardIntroText, setCurrentCardIntroText] = useState<string>("");

  // 카드 부채꼴 드래그/스와이프 관련 상태
  // const [fanRotationOffset, setFanRotationOffset] = useState<number>(0); // 회전 대신 수평 이동 사용
  const [fanHorizontalOffset, setFanHorizontalOffset] = useState<number>(0);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragStartX, setDragStartX] = useState<number>(0);

  // 카드 상세 정보 모달 관련 상태
  const [showCardDetailModal, setShowCardDetailModal] = useState<boolean>(false);
  const [modalCardDetails, setModalCardDetails] = useState<TarotCard | null>(null);

  const [userCredits, setUserCredits] = useState<number | null>(null);
  const { isLoggedIn, user, updateUserCredits, token } = useAuth();

  // const meditationIntroTexts = [ // This will be dynamic based on selectedFortuneType
  //   "잠시 우주의 기운을 느껴보세요...",
  //   `오늘 ${userName}님을 위한 별의 메시지가 준비되고 있습니다.`,
  //   "마음의 준비가 되셨다면, 아래 버튼을 눌러주세요."
  // ];

  const 宇宙背景Ref = useRef<HTMLDivElement>(null);

  // Add a ref for the spread container
  const spreadContainerRef = useRef<HTMLDivElement>(null);
  // State to store the container dimensions

  // 카드 애니메이션 관련 상태 추가
  const [cardsInStartPosition, setCardsInStartPosition] = useState<boolean>(true);
  const [cardsMovingToPosition, setCardsMovingToPosition] = useState<boolean>(false);
  const [currentMovingCardIndex, setCurrentMovingCardIndex] = useState<number | null>(null);

  // 켈틱 크로스 두번째 카드의 회전 상태를 관리하는 state 추가
  const [celticCrossSecondCardRotating, setCelticCrossSecondCardRotating] = useState(false);
  
  // Manager에서 설정한 스프레드 위치 데이터를 저장하는 state
  const [managerSpreadPositions, setManagerSpreadPositions] = useState<{[key: string]: ManagerSpreadPosition[]}>({});
  
  // Manager 스프레드 데이터 로드
  const loadManagerSpreads = async () => {
    try {
      const response = await fetch('/api/tarot/spreads/active');
      const data = await response.json();
      
      if (data.success) {
        const positionsMap: {[key: string]: ManagerSpreadPosition[]} = {};
        
        data.spreads.forEach((spread: any) => {
          if (spread.positions) {
            const positions = Array.isArray(spread.positions) 
              ? spread.positions 
              : JSON.parse(spread.positions || '[]');
            positionsMap[spread.spreadType] = positions;
          }
        });
        
        setManagerSpreadPositions(positionsMap);
      }
    } catch (error) {
      console.error('Manager 스프레드 데이터 로드 실패:', error);
    }
  };

  useEffect(() => {
    loadManagerSpreads();
    
    // 창 포커스 시 Manager 스프레드 데이터 새로고침
    const handleFocus = () => {
      loadManagerSpreads();
    };
    
    window.addEventListener('focus', handleFocus);
    
    // 30초마다 자동 새로고침
    const interval = setInterval(loadManagerSpreads, 30000);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
      clearInterval(interval);
    };
  }, []);

  // 활성 스프레드 데이터 로드
  const loadActiveSpreads = async () => {
    try {
      const response = await fetch('/api/tarot/spreads/active');
      const data = await response.json();
      
      if (data.success) {
        setApiSpreads(data.spreads);
        setIsLoadingSpreads(false);
      } else {
        console.error('활성 스프레드 로드 실패:', data.message);
        // 실패시 빈 데이터 사용
        setApiSpreads([]);
        setIsLoadingSpreads(false);
      }
    } catch (error) {
      console.error('스프레드 데이터 로드 실패:', error);
      // 에러시 빈 데이터 사용
      setApiSpreads([]);
      setIsLoadingSpreads(false);
    }
  };

  useEffect(() => {
    loadActiveSpreads();
    
    // 창 포커스 시 스프레드 목록 새로고침
    const handleFocus = () => {
      loadActiveSpreads();
    };
    
    window.addEventListener('focus', handleFocus);
    
    // 30초마다 자동 새로고침 (Manager 변경사항 실시간 반영)
    const interval = setInterval(loadActiveSpreads, 30000);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
      clearInterval(interval);
    };
  }, []);

  // 카드 Z-Index 관리를 위한 상태 추가
  const [cardZIndices, setCardZIndices] = useState<number[]>([]);
  const baseZIndex = 1000; // 기본 z-index

  // 현재 공개/소개 중인 카드와 하이라이트(확대/아웃라인) 효과 카드를 분리하기 위한 상태 추가
  const [highlightedCardIndex, setHighlightedCardIndex] = useState<number | null>(null);

  // 카드 배치 및 애니메이션 관련 상태 추가
  const [cardsInitializing, setCardsInitializing] = useState<boolean>(true); // 카드가 처음 겹쳐진 상태인지
  const [fanAnimationComplete, setFanAnimationComplete] = useState<boolean>(false); // 부채꼴 애니메이션 완료 여부

  // 추가 상태 변수: 최종 해석 준비 중 메시지 표시 여부
  const [showFinalInterpretationLoading, setShowFinalInterpretationLoading] = useState<boolean>(false);

  // 선택된 스프레드 ID 저장
  const [selectedSpreadId, setSelectedSpreadId] = useState<string | null>(null);

  // API에서 활성화된 스프레드 데이터 상태
  const [apiSpreads, setApiSpreads] = useState<any[]>([]);
  const [isLoadingSpreads, setIsLoadingSpreads] = useState<boolean>(true);
  
  // activeSpreads 별칭 (카드 소개 템플릿에서 사용)
  const activeSpreads = apiSpreads;

  // 사운드 관련 함수들은 공통 훅에서 제공됨

  useEffect(() => {
    if (currentView === 'loginRequiredMessage' && isLoggedIn) {
      setCurrentView('nameInput'); 
    }
    if (currentView === 'creditsRequiredMessage' && user && user.credits >= YEAR_FORTUNE_COST) {
        setCurrentView('nameInput');
    }
  }, [isLoggedIn, user, currentView]);

  // useEffect to handle the end of the space entry animation
  useEffect(() => {
    let timer: number; // Changed from ReturnType<typeof setTimeout>
    if (isEnteringSpace) {
      // Reset button visibility state when animation starts
      setShowMeditationButton(false);
      timer = setTimeout(() => {
        setIsEnteringSpace(false);
        setCurrentView('meditationIntro');
      }, 2500); 
    }
    return () => clearTimeout(timer);
  }, [isEnteringSpace]);

  useEffect(() => {
    if (currentView === 'meditationIntro') {
      // Define meditationIntroTexts here, so it has access to current userName and userConcern
      const isYearFortune = selectedFortuneType === 'year';
      // const isCustomFortune = selectedFortuneType === 'custom'; // Old
      const isCustomFortune = ['customThreeCard', 'customFiveCard', 'customSevenCard', 'customTenCard'].includes(selectedFortuneType as string);

      let text1 = "잠시 우주의 기운을 느껴보세요..."; // Default for today
      let text2 = `${userName}님을 위한 별의 메시지가 준비되고 있습니다.`; // Default for today
      let text3 = "마음의 준비가 되셨다면, 아래 버튼을 눌러주세요."; // Default for today

      if (isYearFortune) {
        text1 = "한 해의 지혜를 구하기 전, 잠시 우주의 거대한 흐름을 느껴보세요...";
        text2 = `이제 ${userName}님의 다가올 한 해를 위한 별들의 속삭임이 준비되고 있습니다.`;
        text3 = "깊은 심호흡과 함께, 아래 버튼을 눌러 운명의 카드들을 맞이하세요.";
      } else if (isCustomFortune) {
        text1 = "당신의 질문이 우주에 닿았습니다...";
        text2 = `"${userConcern}" 에 대한 답을 찾기 위해, 별들이 정렬되고 있습니다.`;
        text3 = "마음이 평온해지면, 카드를 선택하여 그들의 속삭임을 들어보세요.";
      }
      const currentMeditationTexts = [text1, text2, text3];

      setMeditationTexts(currentMeditationTexts);
      setMeditationTextIndex(0);
      setShowMeditationButton(false);
      // Start typing animation for the first text
      // TypingText component will handle subsequent texts via onComplete
    } else if (currentView === 'cardCountSelection') {
      // Initialize for card selection view
      setSelectedCards([]);
      setShowCardSelectionButton(false);
      // Add any other necessary initializations for this view
    }
  }, [currentView]);

  // useEffect to start auto-reveal when the reveal view becomes active
  useEffect(() => {
    if (currentView === 'cardRevealAndInterpretation') {
      // Add a delay to allow the view transition/fade-in animation to complete
      const timer: number = setTimeout(() => { // Explicitly typed timer as number
        setAutoRevealActive(true);
      }, 1200); // Adjust delay as needed (e.g., slightly longer than fade-in)

      return () => clearTimeout(timer); // Cleanup timer on unmount or view change
    } else {
      // Reset autoRevealActive when leaving the reveal view
      setAutoRevealActive(false);
    }
  }, [currentView]);

  const resetTodayFortuneStates = () => {
    // This function resets states primarily related to a single fortune-telling session
    //setSelectedFortuneType(null); // Correctly reset in handleStartOver
    setCardsForSelection([]);
    setSelectedCards([]);
    setRevealedCardsInfo([]);
    setCurrentCardSelectionStep(0); 
    setCurrentCardRevealStep(0);
    setFinalInterpretationText(null);
    setChatMessages([]);
    setFortuneInterpretation(null); 
    setApiError(null);
    setCooldownMessageDetail('');
    setCurrentRevealingCard(null);
    setCardFlipStates([]);
    setIsIntroducingCard(false);
    setCurrentCardIntroText('');
    setAutoRevealActive(false);
    setUserConcern(''); // Reset user concern

    // Remove the transition call from the reset function itself.
    // The caller (e.g., handleStartOver) should decide the next view.
    // transitionToView('nameInput'); // <--- REMOVE THIS LINE
  };

  const handleFortuneTypeSelect = async (type: 'today' | 'year') => {
    setSelectedFortuneType(type);

    // Reset states for a new session
    setChatMessages([]);
    setSelectedCards([]);
    setRevealedCardsInfo([]);
    setFinalInterpretationText(null);
    setApiError(null);
    setCooldownMessageDetail('');
    setCurrentCardSelectionStep(1);
    setCurrentCardRevealStep(0);
    setCurrentRevealingCard(null);
    setCardFlipStates([]);
    setIsIntroducingCard(false);
    setCurrentCardIntroText('');
    setAutoRevealActive(false);
    setIsLoadingFortune(false); // Reset loading state

    if (type === 'today') {
      setIsLoadingFortune(true);
      try {
        const fortuneApiType = 'todayThreeCard'; // '오늘의 운세'는 3카드 스프레드로 가정
        const headers: HeadersInit = {};
        if (token) { // 로그인 상태면 토큰 포함
          headers['Authorization'] = `Bearer ${token}`;
        }
        const response = await fetch(`/api/fortune/pre-check?fortuneType=${fortuneApiType}`, {
          headers,
        });
        const preCheckData = await response.json();
        setIsLoadingFortune(false);

        console.log('Today Pre-check Response:', preCheckData); // 응답 로깅 추가

        // 응답 코드가 오류인 경우 (404, 500 등) 처리
        if (!response.ok && response.status !== 400) {
            console.error(`[Critical Error] 오늘의 운세 Pre-check 오류: ${response.status}`, preCheckData);
            setApiError(preCheckData.error || '운세 가능 여부 확인 중 오류가 발생했습니다.');
            transitionToView('typeSelection');
            return;
        }

        // isAllowed가 false인 경우 처리 (응답 코드가 200이어도)
        if (!preCheckData.isAllowed) {
          console.log('[Pre-check Failed] 이유:', preCheckData.reason, preCheckData);
          
          if (preCheckData.reason === 'cooldown_active') {
            const { hours = 0, minutes = 0 } = preCheckData.remainingTime || {};
            const cooldownMsg = `이미 오늘의 운세를 보셨습니다.\n약 ${hours}시간 ${minutes}분 후에 다시 시도해주세요.`;
            setCooldownMessageDetail(cooldownMsg);
            transitionToView('cooldownMessage');
          } else if (preCheckData.reason === 'user_not_found') {
            console.error('[User Error] 사용자 정보를 찾을 수 없음 (오늘의 운세)');
            // 비로그인 사용자인 경우 이 오류가 나올 수 있으므로, 로그인 요청
            transitionToView('loginRequiredMessage');
          } else {
            setApiError(preCheckData.error || '알 수 없는 이유로 운세를 볼 수 없습니다.');
            transitionToView('typeSelection');
          }
          return;
        }
        
        // 모든 검증을 통과하면 이름 입력으로 진행
        console.log('[Pre-check Success] 오늘의 운세 진행 가능');
        transitionToView('nameInput');

      } catch (error) {
        setIsLoadingFortune(false);
        console.error('Today Pre-check API Error:', error);
        setApiError('운세 가능 여부 확인 중 네트워크 오류가 발생했습니다.');
        transitionToView('typeSelection');
        return;
      }
    } else if (type === 'year') {
      if (!isLoggedIn || !token) { 
        transitionToView('loginRequiredMessage');
        return;
      }

      setIsLoadingFortune(true); 
      try {
        const response = await fetch(`/api/fortune/pre-check?fortuneType=yearFiveCard`, { 
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        const preCheckData = await response.json();
        setIsLoadingFortune(false); 

        console.log('Year Pre-check Response:', preCheckData); // 응답 로깅 추가
        
        // 응답 처리 수정 - HTTP 상태 코드뿐만 아니라 응답 내용도 함께 확인
        // Note: 서버가 200 OK를 반환하지만 isAllowed=false일 수 있음
        
        // 비정상 HTTP 응답 처리
        if (!response.ok) {
            console.error(`[Critical Error] 올해의 운세 Pre-check HTTP 오류: ${response.status}`, preCheckData);
            if (response.status === 404) {
              console.error('[User Error] 사용자 정보 없음:', preCheckData);
              transitionToView('loginRequiredMessage');
              return;
            }
            setApiError(preCheckData.error || '운세 가능 여부 확인 중 오류가 발생했습니다.');
            transitionToView('typeSelection'); 
            return;
        }

        // 정상 HTTP 응답이지만 isAllowed=false인 경우 처리
        if (!preCheckData.isAllowed) {
          console.log('[Pre-check Failed] 이유:', preCheckData.reason, preCheckData);
          
          if (preCheckData.reason === 'insufficient_credits') {
            // 서버로부터 받은 최신 크레딧으로 AuthContext를 통해 업데이트 시도
            if (updateUserCredits && typeof preCheckData.currentCredits === 'number') {
              updateUserCredits(preCheckData.currentCredits);
            }
            console.log('[Credit Check] 크레딧 부족 - 필요:', YEAR_FORTUNE_COST, '보유:', user?.credits, '메시지 화면으로 이동');
            transitionToView('creditsRequiredMessage');
            return; // 명시적으로 함수 실행 종료
          } else if (preCheckData.reason === 'cooldown_active') {
            const { hours = 0, minutes = 0 } = preCheckData.remainingTime || {};
            const cooldownMsg = `이미 해당 운세를 보셨습니다. 약 ${hours}시간 ${minutes}분 후에 다시 시도해주세요.`;
            setCooldownMessageDetail(cooldownMsg);
            transitionToView('cooldownMessage');
            return; // 명시적으로 함수 실행 종료
          } else if (preCheckData.reason === 'user_not_found') { 
            console.error('[Critical Error] 사용자 정보를 찾을 수 없음 (올해의 운세)');
            transitionToView('loginRequiredMessage'); 
            return; // 명시적으로 함수 실행 종료
          } else if (preCheckData.reason === 'login_required_for_paid_fortune') {
            transitionToView('loginRequiredMessage');
            return; // 명시적으로 함수 실행 종료
          } else {
            setApiError(preCheckData.error || '알 수 없는 이유로 운세를 볼 수 없습니다.');
            transitionToView('typeSelection'); 
            return; // 명시적으로 함수 실행 종료
          }
        }
        
        // isAllowed가 true이더라도 데이터를 추가 검증
        if (!preCheckData.currentCredits && preCheckData.currentCredits !== 0) {
          console.error('[Critical Error] 올해의 운세 Pre-check 성공했으나 credits 정보 없음', preCheckData);
          setApiError('사용자 정보를 제대로 불러오지 못했습니다. 다시 로그인해주세요.');
          transitionToView('loginRequiredMessage');
          return;
        }
        
        // 모든 검증을 통과하면 이름 입력으로 진행
        console.log('[Pre-check Success] 올해의 운세 진행 가능:', preCheckData);
        transitionToView('nameInput');

      } catch (error) {
        setIsLoadingFortune(false);
        console.error('Year Pre-check API Error:', error);
        setApiError('운세 가능 여부 확인 중 네트워크 오류가 발생했습니다.');
        transitionToView('typeSelection'); 
        return;
      }
    }
    // Proceed to name input for 'today' or if 'year' pre-check passed (이 부분은 각 if 블록 내부로 이동)
    // transitionToView('nameInput'); // This line is now redundant due to logic inside if blocks
  };

  const handleNameSubmit = () => {
    if (!userName.trim()) {
      addChatMessage('이름을 알려주셔야 진행할 수 있어요. 다시 한번 입력해주시겠어요?', 'system');
      return;
    }

    // 일반적인 가명/닉네임 목록 체크
    const genericNames = ['홍길동', '김아무개', '닉네임', '테스트', '1234', 'aaaa', 'test', 'asdf', '유저', 'user', '익명'];
    if (genericNames.includes(userName.trim().toLowerCase())) {
      // 실명 사용 권장 메시지 (강제하지 않고 안내만 함)
      addChatMessage('타로의 에너지는 이름과 깊이 연결됩니다. 진실된 실명을 사용하시면 더 명확하고 개인화된 해석을 받으실 수 있어요. 그대로 진행하시겠어요?', 'system');
      // 3초 후 자동으로 다음 단계로 진행
      setTimeout(() => {
        transitionToView('meditationIntro');
      }, 3000);
      return;
    }

    // playSound(SOUND_PATHS.nameSubmit);
    // Proceed to meditation/intro view for both types now
    transitionToView('meditationIntro');
  };

  const handleConcernSubmit = async () => {
    if (!userConcern.trim()) {
      addChatMessage('고민 내용을 입력해주세요. 당신의 질문이 별들의 답을 이끌어낼 거예요.', 'system');
      return;
    }
    
    const characterCount = userConcern.trim().length;
    if (characterCount < 30 || characterCount > 500) {
      addChatMessage('고민 내용은 최소 30자에서 최대 500자 사이로 입력해주세요.', 'system');
      return;
    }
    
    if (!isLoggedIn || !token) {
      transitionToView('loginRequiredMessage');
      return;
    }

    // 중복 요청 방지를 위한 플래그 확인
    if (isLoadingFortune) {
      console.log("[Tarot] 이미 요청 처리 중입니다. 중복 요청 방지");
      return;
    }

    setIsLoadingFortune(true);
    
    try {
      // requestId를 생성하여 각 요청을 식별할 수 있게 함
      const requestId = `pre-check-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      console.log(`[Tarot] 고민 기반 타로 Pre-check 요청 시작 (${requestId})`);
      
      //  Ensure the API endpoint and fortuneType string match backend expectations for custom readings
      const response = await fetch(`/api/fortune/pre-check?fortuneType=customThreeCard&_=${requestId}`, { // 캐시 방지 파라미터 추가
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Request-ID': requestId, // 요청 식별자 헤더 추가
        },
        // 캐시를 사용하지 않도록 설정
        cache: 'no-store',
      });
      
      const preCheckData = await response.json();
      console.log(`[Tarot] Pre-check 응답 받음 (${requestId})`, preCheckData);
      
      setIsLoadingFortune(false);

      if (!response.ok) {
        console.error(`[Critical Error] 고민 기반 타로 Pre-check HTTP 오류: ${response.status}`, preCheckData);
        setApiError(preCheckData.error || '운세 가능 여부 확인 중 오류가 발생했습니다.');
        // Stay on concernInput or show a generic error view via chat/toast
        addChatMessage(preCheckData.error || '운세 가능 여부 확인 중 오류가 발생했습니다. 다시 시도해주세요.', 'system');
        return;
      }

      if (!preCheckData.isAllowed) {
        if (preCheckData.reason === 'insufficient_credits') {
          if (updateUserCredits && typeof preCheckData.currentCredits === 'number') {
            updateUserCredits(preCheckData.currentCredits);
          }
          // Set selectedFortuneType to 'customThreeCard' (or a relevant default) here so the message renders correctly
          setSelectedFortuneType('customThreeCard'); 
          transitionToView('creditsRequiredMessage');
        } else if (preCheckData.reason === 'cooldown_active') {
          const { hours = 0, minutes = 0 } = preCheckData.remainingTime || {};
          const cooldownMsg = `이미 고민 기반 타로점을 보셨습니다.\n약 ${hours}시간 ${minutes}분 후에 다시 시도해주세요.`;
          setCooldownMessageDetail(cooldownMsg);
          setSelectedFortuneType('customThreeCard'); // For correct message rendering if needed
          transitionToView('cooldownMessage');
        } else {
          setApiError(preCheckData.error || '알 수 없는 이유로 타로점을 볼 수 없습니다.');
          addChatMessage(preCheckData.error || '알 수 없는 이유로 타로점을 볼 수 없습니다. 다시 시도해주세요.', 'system');
        }
        return;
      }
      
      // Pre-check passed
      setSelectedFortuneType('customThreeCard'); // Set a default custom type, will be refined at card count selection
      transitionToView('nameInput'); 

    } catch (error) {
      setIsLoadingFortune(false);
      console.error('Custom Tarot Pre-check API Error:', error);
      const errorMessage = '운세 가능 여부 확인 중 네트워크 오류가 발생했습니다. 잠시 후 다시 시도해주세요.';
      setApiError(errorMessage);
      addChatMessage(errorMessage, 'system');
    }
  };

  // Example of adding a chat message
  const addChatMessage = (text: string, sender: ChatMessage['sender'], associatedCard?: TarotCard) => {
    setChatMessages(prev => [...prev, {
      id: Date.now().toString() + Math.random().toString(), // simple unique id
      sender,
      text,
      card: associatedCard,
      timestamp: Date.now()
    }]);
    if (sender === 'ai') playSound(SOUND_PATHS.chatMessage);
  };
  
  const startCardSelectionPhase = () => {
    const isYearFortune = selectedFortuneType === 'year';
    const isTodayFortune = selectedFortuneType === 'today';
    const isCustomFortune = ['customThreeCard', 'customFiveCard', 'customSevenCard', 'customTenCard'].includes(selectedFortuneType as string);

    let systemMessage = '';
    let nextView: View = 'cardDrawing'; // Default to cardDrawing
    // let cardsToSetup = 0; // Not strictly needed as setCurrentNumCardsToSelect handles the count

    if (isYearFortune) {
      setCurrentNumCardsToSelect(NUM_CARDS_FIVE_SPREAD);
      setCurrentFortuneCost(YEAR_FORTUNE_COST);
      // selectedFortuneType is already 'year'
      systemMessage = `좋습니다. 이제 ${userName}님의 한 해를 관통할 ${NUM_CARDS_FIVE_SPREAD}개의 중요한 메시지를 선택할 시간입니다. 당신의 영혼이 이끄는 카드 ${NUM_CARDS_FIVE_SPREAD}장을 차례대로 선택해주세요.`;
      nextView = 'cardDrawing';
    } else if (isTodayFortune) {
      setCurrentNumCardsToSelect(NUM_CARDS_THREE_SPREAD);
      setCurrentFortuneCost(0); // Assuming today's fortune is free or cost is handled by pre-check
      // selectedFortuneType is already 'today'
      systemMessage = `좋습니다. 이제 당신의 오늘을 들여다볼 시간입니다. 당신의 직감을 믿고, 마음이 이끄는 카드 ${NUM_CARDS_THREE_SPREAD}장을 차례대로 선택해주세요.`;
      nextView = 'cardDrawing';
    } else if (isCustomFortune) {
      const placeholderCardCount = NUM_CARDS_THREE_SPREAD; // For the initial message only
      // 긴 고민 내용은 처리하여 최대 50자만 표시하고 말줄임표(...) 추가
      const shortenedConcern = userConcern.length > 50 ? userConcern.substring(0, 50) + '...' : userConcern;
      systemMessage = `당신의 고민에 대한 답을 찾기 위해, 우선 마음이 이끄는 카드를 선택할 준비를 합니다. 곧이어 실제 카드 장수를 선택하게 됩니다.`;
      nextView = 'cardCountSelection';
    } else {
      console.error("startCardSelectionPhase: Unexpected selectedFortuneType or flow error", selectedFortuneType);
      addChatMessage("운세 유형을 선택하는 과정에서 오류가 발생했습니다. 처음부터 다시 시도해주세요.", "system");
      transitionToView('concernInput'); // Safeguard: Go back to initial state
      return;
    }
    
    addChatMessage(systemMessage, 'system');
    setCardsForSelection(getRandomCards(tarotCardsData, tarotCardsData.length));
    setSelectedCards([]);
    setShowCardSelectionButton(false);
    transitionToView(nextView);
  };

  // This is a new handleCardSelect for the 5-card spread - now adapts to numCardsToSelect
  const handleFiveCardSpreadSelect = (card: TarotCard) => {
    if (selectedCards.length < NUM_CARDS_THREE_SPREAD) { // Use dynamic number
      // 카드 선택 효과음 재생
      playSound(SOUND_PATHS.cardPlace);
      
      setSelectedCards(prev => [...prev, card]);
      setCardsForSelection(prev => prev.filter(c => c.id !== card.id)); // Remove selected card from options
      setCurrentCardSelectionStep(prev => prev + 1);

      if (selectedCards.length === NUM_CARDS_THREE_SPREAD - 1) { // Check if the last card was picked
        // Transition to reveal phase after a short delay
        addChatMessage('모든 카드를 선택하셨습니다. 이제 선택하신 카드들을 하나씩 펼쳐보겠습니다.', 'system');
        setTimeout(() => {
          setCurrentView('cardRevealAndInterpretation');
          setCurrentCardRevealStep(0);
        }, 1500); 
      }
    }
  };

  // This is for revealing cards one by one
  const handleRevealNextCard = async () => {
    // 이 함수는 더 이상 버튼 클릭으로 사용되지 않지만,
    // 자동 카드 공개를 위한 로직은 useEffect로 이동
    // 함수는 하위 호환성을 위해 유지
  };

  const handleStartOver = () => {
    // 음악 중지
    stopSound();
    
    // 1. 최우선으로 모든 운세 관련 데이터 상태를 초기화합니다.
    resetTodayFortuneStates(); // selectedCards, revealedCardsInfo, finalInterpretationText 등 초기화
    setSelectedFortuneType(null); // ★ 매우 중요: 운세 유형을 null로 하여 다른 뷰 조건 방지
    setUserName('');
    setApiError(null);
    setCooldownMessageDetail('');
    setFinalInterpretationText(null); // ★ 명시적으로 다시 한번 null 처리
    setSelectedCards([]); // ★ 명시적으로 다시 한번 빈 배열 처리
    setUserConcern(''); // Reset user concern

    // 2. 즉시 뷰를 concernInput 으로 변경 (이 페이지의 기본 시작점)
    setCurrentView('concernInput');

    // 3. 화면 전환 애니메이션 시작 (컨텐츠 페이드 아웃 -> 오버레이 표시)
    // setContentOpacity(0); // 애니메이션 시작 전에는 현재 뷰가 그대로 보이도록 잠시 대기
    // setShowTransitionOverlay(true);

    // 나머지 상태 초기화 (애니메이션과 직접 관련 없는 UI 상태들)
    setIsEnteringSpace(false);
    setShowMeditationButton(false);
    setMeditationTextIndex(0);
    setShowCardSelectionButton(false);
    setAutoRevealActive(false);
    setCurrentRevealingCard(null);
    setCardFlipStates([]);
    setIsIntroducingCard(false);
    setCurrentCardIntroText('');
    setAutoRevealActive(false);
    setUserConcern(''); // Reset user concern
    setCelticCrossSecondCardRotating(false);

    // 4. 짧은 지연 후 페이드 아웃 및 오버레이 표시 (뷰 변경이 반영될 시간)
    setTimeout(() => {
      setContentOpacity(0);
      setShowTransitionOverlay(true);

      // 5. 페이드 아웃 및 오버레이 표시 후 새 뷰의 컨텐츠 페이드 인
      setTimeout(() => {
        setContentOpacity(1);
        
        // 6. 카드 공개 화면이면 자동 카드 공개 시작
        if (currentView === 'cardRevealAndInterpretation') {
          setTimeout(() => {
            setAutoRevealActive(true);
          }, 1000);
        }
        
        // 7. 컨텐츠가 표시된 후 오버레이 숨기기
        setTimeout(() => {
          setShowTransitionOverlay(false);
        }, 800);
      }, 400);
    }, 800);
  };

  const handleDirectReturnToTypeSelection = () => {
    // 음악 중지
    stopSound();
    
    resetTodayFortuneStates();
    setSelectedFortuneType(null);
    setUserName('');
    setApiError(null);
    setCooldownMessageDetail('');
    setFinalInterpretationText(null);
    setSelectedCards([]);
    setUserConcern(''); // Reset user concern
    
    // Directly set view to concernInput (this page's default)
    setCurrentView('concernInput');
    setContentOpacity(1);
    setShowTransitionOverlay(false);

    // Reset other UI states that might have been active
    setIsEnteringSpace(false);
    setShowMeditationButton(false);
    setMeditationTextIndex(0);
    setShowCardSelectionButton(false);
    setAutoRevealActive(false);
    setCurrentRevealingCard(null);
    setCardFlipStates([]);
    setIsIntroducingCard(false);
    setCurrentCardIntroText('');
    setFanHorizontalOffset(0); 
    setCelticCrossSecondCardRotating(false);
  };
  
  // 메인 페이지로 이동하는 함수 추가
  const handleNavigateToHome = () => {
    // 음악 중지
    stopSound();
    
    // 메인 페이지(홈)로 이동
    window.location.href = '/';
  };

  const renderMeditationIntro = () => {
    const isYearFortune = selectedFortuneType === 'year';
    // const isCustomFortune = selectedFortuneType === 'custom'; // Old
    const isCustomFortune = ['customThreeCard', 'customFiveCard', 'customSevenCard', 'customTenCard'].includes(selectedFortuneType as string);

    let text1 = "잠시 우주의 기운을 느껴보세요..."; // Default for today
    let text2 = `${userName}님을 위한 별의 메시지가 준비되고 있습니다.`; // Default for today
    let text3 = "마음의 준비가 되셨다면, 아래 버튼을 눌러주세요."; // Default for today

    if (isYearFortune) {
      text1 = "한 해의 지혜를 구하기 전, 잠시 우주의 거대한 흐름을 느껴보세요...";
      text2 = `이제 ${userName}님의 다가올 한 해를 위한 별들의 속삭임이 준비되고 있습니다.`;
      text3 = "깊은 심호흡과 함께, 아래 버튼을 눌러 운명의 카드들을 맞이하세요.";
    } else if (isCustomFortune) {
      text1 = "당신의 질문이 우주에 닿았습니다...";
      // 긴 고민 내용은 처리하여 최대 20자만 표시하고 말줄임표(...) 추가
      const shortenedConcern = userConcern.length > 30 ? userConcern.substring(0, 30) + ' ...' : userConcern;
      text2 = `"${shortenedConcern}" 에 대한 답을 찾기 위해, 별들이 정렬되고 있습니다.`;
      text3 = "마음이 평온해지면, 카드를 선택하여 그들의 속삭임을 들어보세요.";
    }

    return (
      <>
        <h2 style={{ fontSize: '2em', marginBottom: '30px', minHeight: '2.5em' }}>
          <TypingText text={text1} speed={80} startDelay={500} />
        </h2>
        <p style={{ fontSize: '1.4em', marginBottom: '40px', minHeight: '3em' }}>
          <TypingText text={text2} speed={60} startDelay={500 + text1.length * 80 + 300} /> 
        </p>
        <p style={{ fontSize: '1.1em', marginBottom: '50px', minHeight: '2em', opacity: showMeditationButton ? 1 : 0, transition: 'opacity 0.5s ease-in' }}>
           <TypingText 
             text={text3} 
             speed={50} 
             startDelay={500 + (text1.length * 80) + 300 + (text2.length * 60) + 300} 
             onComplete={() => setShowMeditationButton(true)}
           />
        </p>
        {showMeditationButton && (
          <button 
            onClick={startCardSelectionPhase} 
            className="mystical-button"
            style={{ 
              opacity: 0, 
              animation: 'fadeIn 1s forwards 0.5s',
              transform: 'translateY(10px)',
              animationFillMode: 'forwards'
            }}
          >
            운명의 문을 열겠습니다
          </button>
        )}
      </>
    );
  };

  const renderFiveCardSelection = () => {
    const allCardsToDisplay = cardsForSelection;
    const numTotalCards = allCardsToDisplay.length;
    const isMaxCardsSelected = selectedCards.length === currentNumCardsToSelect; // Use dynamic number

    // 화면 크기에 기반한 카드 크기 설정
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;
    const cardWidth = Math.min(120, screenWidth / 20); // 화면 크기에 따라 조정 (더 작게)
    const cardHeight = cardWidth * 1.7; // 높이 비율 조정

    // 부채꼴 레이아웃 설정 - 개선된 부채꼴 계산 (아래쪽으로 향하는 부채꼴)
    const fanWidth = screenWidth * 1.2; // 부채꼴의 전체 너비
    const baseFanAngle = 100; // 전체 부채꼴의 각도 범위(도) - 더 좁게 조정
    const verticalOffset = cardHeight * 1.25; // 카드가 선택될 때 위로 올라가는 높이
    const fanVerticalPosition = screenHeight * 0.3; // 부채꼴의 세로 위치 - 더 위로 조정
    const fanRadius = screenHeight * 0.5; // 부채꼴의 반지름 - 원호 크기 조정

    // 드래그 이벤트 핸들러
    const handleDragStart = (clientX: number) => {
      setIsDragging(true);
      setDragStartX(clientX);
    };

    const handleDragMove = (clientX: number) => {
      if (!isDragging) return;
      const dragDeltaX = clientX - dragStartX;
      // 수평 이동량 적용 (값을 줄이면 덜 민감하게, 늘리면 더 민감하게)
      const moveAmount = dragDeltaX * 1.5; // 1px 드래그당 1.5px 이동
      setFanHorizontalOffset(prevOffset => {
        // 이동 범위 제한 (화면 너비의 일정 비율)
        const maxOffset = screenWidth * 0.3;
        const newOffset = prevOffset + moveAmount;
        return Math.max(-maxOffset, Math.min(maxOffset, newOffset));
      });
      setDragStartX(clientX); 
    };

    const handleDragEnd = () => {
      setIsDragging(false);
    };

    // 터치 이벤트 래퍼
    const handleTouchStart = (e: React.TouchEvent) => {
      if (e.touches.length === 1) { 
        handleDragStart(e.touches[0].clientX);
      }
    };
    const handleTouchMove = (e: React.TouchEvent) => {
      if (e.touches.length === 1) {
        handleDragMove(e.touches[0].clientX);
      }
    };
    const handleTouchEnd = () => {
      handleDragEnd();
    };

    // 마우스 이벤트 래퍼
    const handleMouseDown = (e: React.MouseEvent) => {
      e.preventDefault(); 
      handleDragStart(e.clientX);
    };
    const handleMouseMove = (e: React.MouseEvent) => {
      handleDragMove(e.clientX);
    };
    const handleMouseUp = () => {
      handleDragEnd();
    };

    // 선택된 카드에 적용할 빛나는 테두리 애니메이션 스타일
    const glowingBorderAnimation = `
      @keyframes glowingBorder {
        0% {
          box-shadow: 0 0 5px #ffeb3b, 0 0 10px #ffeb3b, 0 0 15px rgba(255, 235, 59, 0.6);
        }
        50% {
          box-shadow: 0 0 10px #ffeb3b, 0 0 20px #ffeb3b, 0 0 30px rgba(255, 235, 59, 0.8);
        }
        100% {
          box-shadow: 0 0 5px #ffeb3b, 0 0 10px #ffeb3b, 0 0 15px rgba(255, 235, 59, 0.6);
        }
      }
    `;

    return (
      <div className="flex flex-col items-center justify-center h-full text-white relative">
        <style>
          {glowingBorderAnimation}
          {`
            .card-fan-container {
              cursor: grab;
              touch-action: pan-y; /* 수직 스크롤은 유지, 수평 스와이프만 감지 */
            }
            .card-fan-container.dragging {
              cursor: grabbing;
            }
          `}
        </style>
        
        <div className="text-center mb-8">
          <TypingText
            text={`카드 ${currentNumCardsToSelect}장을 천천히 그리고 신중히 선택해주세요.`} // Updated text to use currentNumCardsToSelect
            speed={48}
            className="text-2xl"
            onComplete={() => {}}
          />
        </div>

        {/* 카드 컨테이너 */}
        <div 
          className={`relative w-full card-fan-container ${isDragging ? 'dragging' : ''}`}
          style={{ 
            height: `${fanVerticalPosition + cardHeight * 2}px`, 
            overflow: 'hidden', 
            WebkitUserSelect: 'none', 
            MozUserSelect: 'none', 
            msUserSelect: 'none', 
            userSelect: 'none', 
            marginTop: '10vh', // 위쪽 여백 추가
          }}
          onMouseDown={handleMouseDown}
          onMouseMove={isDragging ? handleMouseMove : undefined}
          onMouseUp={handleMouseUp}
          onMouseLeave={isDragging ? handleMouseUp : undefined} 
          onTouchStart={handleTouchStart}
          onTouchMove={isDragging ? handleTouchMove : undefined}
          onTouchEnd={handleTouchEnd}
          onTouchCancel={handleTouchEnd}
        >
          {/* 표시할 카드 수 제한 (최대 78장) */}
          {allCardsToDisplay.slice(0, 78).map((card, index, displayedCards) => {
            // 표시되는 카드 배열의 길이 기준으로 정규화 (실제 표시되는 카드만 고려)
            const normalizedIndex = displayedCards.length > 1 ? index / (displayedCards.length - 1) : 0.5;
            const relativePosition = normalizedIndex * 2 - 1; 
            
            // 카드 위치 조정을 위한 계수 (중앙에 더 많은 카드가 표시되도록)
            const positionAdjustment = Math.pow(Math.abs(relativePosition), 0.85) * Math.sign(relativePosition);
            
            // 개선된 부채꼴 레이아웃 계산 (조정된 위치 사용)
            const currentCardAngle = positionAdjustment * (baseFanAngle / 2); 
            
            // 부채꼴 아크 계산 (원형 레이아웃으로 변경) - 아래쪽으로 향하도록 수정
            const angleRadians = (currentCardAngle * Math.PI) / 180;
            const cardX = Math.sin(angleRadians) * fanRadius;
            const cardY = Math.cos(angleRadians) * fanRadius; // 부호 변경하여 아래쪽으로 향하게 변경
            
            // 카드 간격 조정을 위한 추가 간격 (가장자리로 갈수록 더 간격 벌어짐)
            const spacingFactor = 2.25 + Math.abs(positionAdjustment) * 0.1;
            
            // 수평 오프셋 적용
            const finalTranslateX = cardX * spacingFactor + fanHorizontalOffset;
            
            // Z-인덱스: 좌측(낮은 인덱스)부터 우측(높은 인덱스)로 갈수록 위에 표시
            const zIndex = 100 + index; // 인덱스가 커질수록(우측으로 갈수록) 위에 표시
            const isSelected = selectedCards.some(sc => sc.id === card.id);
            
            const cardStyle = {
              position: 'absolute',
              left: '50%', // 중심점은 화면 중앙
              bottom: isSelected ? `${20 + verticalOffset}px` : '20px', // 선택된 카드는 실제 위치를 위로 이동
              width: `${cardWidth}px`,
              height: `${cardHeight}px`,
              transformOrigin: 'bottom center',
              transform: `translateX(calc(-50% + ${finalTranslateX}px)) translateY(-${cardY}px) rotate(${currentCardAngle}deg) ${isSelected ? 'scale(1.1)' : ''}`,
              transition: isDragging ? 'none' : 'all 0.3s ease-out', // all로 변경하여 bottom도 애니메이션
              zIndex: isSelected ? 2000 : zIndex, // 선택된 카드의 z-index를 더 높게 설정
              cursor: isMaxCardsSelected && !isSelected ? 'default' : 'pointer',
              filter: `brightness(${1 - Math.abs(positionAdjustment) * 0.3})`, // 중앙에서 벗어날수록 어두워짐
              marginLeft: `${Math.abs(positionAdjustment) * 2}px`, // 약간의 추가 공간
            };
            
            // 카드 이미지 스타일 - 선택 시에도 뒷면을 계속 보여줌
            const imageStyle: React.CSSProperties = {
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              borderRadius: '8px',
              boxShadow: isSelected 
                ? '0 8px 32px rgba(255, 235, 59, 0.5)' // 선택된 카드에 황금색 그림자
                : `0 4px 10px rgba(0, 0, 0, ${0.4 + Math.abs(positionAdjustment) * 0.2})`,
              transition: 'all 0.25s ease-out',
              filter: isSelected 
                ? 'brightness(1.2)' 
                : isMaxCardsSelected && !isSelected 
                  ? 'brightness(0.5) saturate(0.4)' 
                  : `brightness(${0.9 + Math.abs(positionAdjustment) * 0.1})`,
              opacity: isMaxCardsSelected && !isSelected ? 0.4 : 1,
              border: isSelected 
                ? '2px solid #FFEB3B' 
                : `1px solid rgba(255,255,255,${0.2 + Math.abs(positionAdjustment) * 0.1})`,
              animation: isSelected ? 'glowingBorder 1.5s infinite' : 'none',
            };

            // 카드 호버 효과 핸들러 - 이제 컨테이너에서 처리
            const handleCardMouseEnter = (e: React.MouseEvent<HTMLDivElement>) => {
              if (!isDragging && (!isMaxCardsSelected || isSelected)) { // 드래그 중이 아닐 때 호버 효과
                const currentTransform = e.currentTarget.style.transform || '';
                if (!isSelected) {
                  e.currentTarget.style.transform = currentTransform + ' scale(1.05)';
                  e.currentTarget.style.filter = 'brightness(1.2)';
                  e.currentTarget.style.zIndex = '1500'; // 높은 z-index로 앞에 표시
                }
              }
            };
            
            const handleCardMouseLeave = (e: React.MouseEvent<HTMLDivElement>) => {
              if (!isDragging) { // 드래그 중이 아닐 때 호버 효과 복원
                if (!isSelected) {
                  // 원래 transform 복원
                  e.currentTarget.style.transform = `translateX(calc(-50% + ${finalTranslateX}px)) translateY(-${cardY}px) rotate(${currentCardAngle}deg)`;
                  e.currentTarget.style.filter = `brightness(${1 - Math.abs(positionAdjustment) * 0.3})`;
                  e.currentTarget.style.zIndex = zIndex.toString(); // 원래 z-index로 되돌림
                }
              }
            };

            return (
              <div 
                key={card.id} 
                style={cardStyle as React.CSSProperties} 
                onClick={() => !isDragging && (!isMaxCardsSelected || isSelected) ? handleCardClick(card) : null}
                onMouseEnter={handleCardMouseEnter}
                onMouseLeave={handleCardMouseLeave}
              >
                <img
                  src="/images/tarot/CardBacks.jpg" 
                  alt={card.name}
                  style={imageStyle}
                  draggable="false" 
                />
              </div>
            );
          })}
        </div>

        {/* 선택된 카드 카운터 및 버튼 */}
        <div className="mt-8 flex flex-col items-center">
          <div className="mb-4 text-lg">
            선택한 카드: {selectedCards.length} / {currentNumCardsToSelect} {/* Use dynamic number */}
          </div>
          
          {showCardSelectionButton && (
            <button
              onClick={handleConfirmCards}
              className="mystical-button"
              style={{
                animation: 'fadeIn 0.5s ease-out, glowPulse 5s infinite',
              }}
            >
              운명 살펴보기
            </button>
          )}
        </div>
      </div>
    );
  };

  const renderCardReveal = () => {
    const cardsToDisplay = selectedCards; // Use selectedCards directly
    const totalCardsInSpread = cardsToDisplay.length;
    const isCelticCross = selectedFortuneType === 'customTenCard';

    // Define a fixed size for the card rendering area or a percentage that adapts to the screen
    const spreadAreaWidthStr = window.innerWidth > 1200 ? '1000px' : '90%';
    const spreadAreaHeightStr = '500px';

    // 숫자 값으로 변환 (getSpreadLayoutStyles 함수용)
    const spreadAreaWidth = window.innerWidth > 1200 ? 1000 : window.innerWidth * 0.9;
    const spreadAreaHeight = 500;

    // The container style with responsive values
    const spreadContainerStyle: React.CSSProperties = {
      position: 'relative',
      width: spreadAreaWidthStr,
      height: spreadAreaHeightStr,
      margin: '20px auto',
    };

    return (
      <div className="card-reveal-container" style={{ maxWidth: '1200px', margin: '0 auto', position: 'relative' }}>
        <style>{sceneTransitionStyles + tarotCardStyles}</style>
        {/* ... existing header and progress bar ... */}
        <div style={{ opacity: isIntroducingCard ? 0.3 : 1, filter: isIntroducingCard ? 'blur(3px)' : 'none', transition: 'opacity 0.5s, filter 0.5s' }}>
          <div className="card-reveal-header text-center mb-8">
            <h3 className="text-2xl mb-2" style={{ fontWeight: '300', color: '#f0f0f0' }}>
              {finalInterpretationText 
                ? "모든 카드가 공개되었습니다" 
                : currentRevealingCard !== null && cardFlipStates[currentRevealingCard]
                  ? `${currentRevealingCard + 1}번째 카드를 살펴보고 있습니다...` 
                  : cardsMovingToPosition
                    ? "카드가 배열되고 있습니다..." 
                  : "카드를 공개합니다..."}
            </h3>
            <div className="card-progress" style={{ display: 'flex', justifyContent: 'center', marginTop: '20px' }}>
              {Array.from({ length: totalCardsInSpread }).map((_, idx) => (
                <div key={idx} style={{
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  margin: '0 4px',
                  marginBottom: '30px',
                  background: cardFlipStates[idx] ? '#ffeb3b' : 'rgba(255, 255, 255, 0.3)',
                  transition: 'background 0.3s ease'
                }} />
              ))}
            </div>
          </div>

          {/* Updated Spread Container with ref */}
          <div 
            ref={spreadContainerRef}
            className="spread-layout-area" 
            style={spreadContainerStyle}
          >
            {cardsToDisplay.map((card, index) => {
              const isCrossingCard = isCelticCross && index === 1;
              // Determine if this specific card is the one currently being animated from the deck
              const isActivelyMoving = cardsMovingToPosition && currentMovingCardIndex === index;
              // Determine if this card has started its move or is in the process of moving
              const hasOrIsMoving = cardsMovingToPosition && currentMovingCardIndex !== null && index <= currentMovingCardIndex;

              const cardW = window.innerWidth <= 576 ? 60 : window.innerWidth <= 768 ? 70 : 100;
              const cardH = cardW * 1.6;
              
              let finalCardW = cardW;
              let finalCardH = cardH;
              if (selectedFortuneType === 'customTenCard' || selectedFortuneType === 'customCelticCross') {
                finalCardW = cardW * 0.85;
                finalCardH = cardH * 0.85;
              }
              
              // A card is in deck position if it hasn't started moving yet.
              const isInDeckPosition = cardsInStartPosition && !hasOrIsMoving;

              const styleProps = isInDeckPosition
                ? getCardStartPosition(index, totalCardsInSpread) 
                : getSpreadLayoutStyles(
                    selectedFortuneType,
                    index,
                    totalCardsInSpread,
                    undefined, // fiveCardSpreadType는 이 컨텍스트에서 사용되지 않음
                    spreadAreaWidth,
                    spreadAreaHeight,
                    managerSpreadPositions[selectedFortuneType as string] || undefined
                  );
              
              let currentTransform = styleProps.transform || '';
              let currentZIndex: number;

              if (isActivelyMoving) {
                // The card currently flying from the deck to its position gets the highest z-index
                currentZIndex = baseZIndex + 1000; 
              } else {
                // Landed cards or cards still in deck use the z-index from the state
                currentZIndex = cardZIndices[index] !== undefined ? cardZIndices[index] : baseZIndex + index;
              }
              
              let currentTransition = hasOrIsMoving
                ? 'all 1.8s cubic-bezier(0.19, 1, 0.22, 1)' // Transition for moving cards
                : 'transform 0.3s ease, box-shadow 0.3s ease'; // Default transition for non-moving cards

              if (highlightedCardIndex === index) {
                let scaleFactor = 1.05;
                if (window.innerWidth <= 576) {
                  scaleFactor = 1.15; 
                } else if (window.innerWidth <= 768) {
                  scaleFactor = 1.1;
                }
                currentTransform = `${currentTransform} scale(${scaleFactor})`;
                // If highlighted, its z-index (once landed) would have been set high by moveNextCard
              }
                  
              return (
              <div 
                key={card.id} 
                data-card-index={index}
                className={`
                  ${isInDeckPosition ? 'card-deck-position' : 'card-reveal'} 
                  ${cardFlipStates[index] ? 'card-flipped' : ''} 
                  ${isCrossingCard && celticCrossSecondCardRotating ? 'celtic-cross-crossing' : ''} 
                  ${highlightedCardIndex === index ? 'highlight-effects' : ''} 
                  ${hasOrIsMoving ? 'card-moving' : ''}
                `}
                style={{ 
                    ...styleProps, 
                    transform: currentTransform, 
                    width: `${finalCardW}px`,
                    height: `${finalCardH}px`,
                    zIndex: currentZIndex,
                    transition: currentTransition,
                }}
              >
                <div className="card-inner">
                  <div className="card-back">
                    <img 
                      src="/images/tarot/CardBacks.jpg" 
                      alt="카드 뒷면" 
                      style={{ width: '100%', height: '100%', objectFit: 'cover', borderRadius: '10px' }}
                    />
                  </div>
                  <div className="card-front">
                    <img 
                      src={`/images/tarot/${card.imageName}`} 
                      alt={card.name} 
                      style={{ width: '100%', height: '100%', objectFit: 'cover', borderRadius: '10px' }}
                    />
                  </div>
                </div>
              </div>
              );
            })}
          </div>
        </div>

        {/* ... existing isIntroducingCard and finalInterpretationText logic ... */}

        {/* 개별 카드 소개 텍스트 오버레이 */}      
        {isIntroducingCard && currentCardIntroText && (
          <div style={{
            position: 'fixed', // 화면 전체를 덮도록 fixed 사용
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.6)', // 반투명 검은 배경
            backdropFilter: 'blur(5px)', // 뒷 배경 블러 처리
            zIndex: 100, // 다른 요소들 위에 표시
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column', // 컨텐츠를 세로로 정렬
            padding: '20px',
            animation: 'fadeIn 0.5s ease-out' // 부드러운 등장 효과
          }}>
            {/* 현재 소개 중인 카드 이미지 */}
            {currentRevealingCard !== null && selectedCards[currentRevealingCard] && (
              <img 
                src={`/images/tarot/${selectedCards[currentRevealingCard].imageName}`}
                alt={selectedCards[currentRevealingCard].name}
                style={{
                  width: '150px', // 카드 크기 조절
                  height: 'auto',
                  borderRadius: '10px',
                  boxShadow: '0 10px 25px rgba(0,0,0,0.5)', // 그림자 효과
                  marginBottom: '30px', // 텍스트와의 간격
                  border: '2px solid #fff',
                  animation: 'floatIn 0.7s ease-out' // 부드럽게 떠오르는 효과
                }}
              />
            )}
            <div style={{ textAlign: 'center', color: 'white', maxWidth: '700px' }}>
              {/* TypingText를 HTML 내용을 표시할 수 있도록 수정 */}
              <div className="text-3xl lg:text-4xl font-light leading-relaxed card-intro-text-container">
                <TypingText
                  text={formatHighlightedText(currentCardIntroText)}
                  speed={50} // 타이핑 속도 조절
                  onComplete={handleCardIntroTypingComplete} // 정의한 핸들러 함수 연결
                  key={currentCardIntroText} // 텍스트가 바뀔 때마다 TypingText 재실행
                  useHtml={true} // HTML 내용 사용 플래그 (TypingText 컴포넌트에 추가 필요)
                />
              </div>
            </div>
          </div>
        )}

        {/* 모든 카드 소개 후 최종 해석 준비 중 표시 (isIntroducingCard가 아닐 때만) */}
        {cardFlipStates.every(state => state) && showFinalInterpretationLoading && !isIntroducingCard && (
          <div className="text-center mt-8 interpretation-panel" style={{ // interpretation-panel 클래스 추가
            maxWidth: '700px', 
            margin: '20px auto',
            // background, borderRadius, padding 등은 .interpretation-panel CSS에서 가져옴
          }}>
            <p className="text-xl" dangerouslySetInnerHTML={{ __html: formatHighlightedText("모든 카드의 의미를 종합하여 **최종 해석**을 준비중입니다...") }}></p>
            <div className="loading-stars mt-3" style={{ fontSize: '2rem', display: 'flex', justifyContent: 'center', gap: '10px' }}>
              {[0.1, 0.3, 0.5].map(delay => (
                <span key={delay} style={{ animation: `mysticalShine 1.5s infinite ${delay}s`, display: 'inline-block' }}>
                  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" style={{ width: '30px', height: '30px', fill: '#ffeb3b' }}>
                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2L9.19 8.63L2 9.24l5.46 4.73L5.82 21L12 17.27z" />
                  </svg>
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  // 마크다운 볼드(**텍스트**)를 <span class=\"highlighted-text\">로 변환하는 함수
  const formatHighlightedText = (text: string | null): string => {
    if (!text) return '';
    let formattedText = text;
    // 순서 중요: ##카드 이름##을 먼저 처리하면 ** 내부의 ##가 오작동할 수 있으므로, **를 먼저 처리.
    // 혹은, 더 복잡하지만 안전한 정규식 사용 필요.
    // 여기서는 ** 먼저 처리 가정.

    // **텍스트** 패턴을 찾아서 <span class=\"highlighted-text\">로 교체
    // 정규식을 조금 더 관대하게 수정하여 단어 경계(\B) 없이도 작동하도록 함 (AI 출력이 항상 \B를 만족하지 않을 수 있음)
    formattedText = formattedText.replace(/\*\*([^\*\*]+)\*\*/g, '<span class="highlighted-text">$1</span>');
    
    // ##카드 이름## 패턴을 찾아서 <span class=\"card-name-interactive\" data-card-name=\"카드 이름\">카드 이름</span>으로 교체
    formattedText = formattedText.replace(/##([^#]+)##/g, (match, cardName) => {
      const trimmedCardName = cardName.trim();
      // tarotCardsData에서 실제 카드 객체를 찾아 ID나 key를 사용하는 것이 더 정확하지만,
      // 우선은 이름으로 매칭 시도.
      return `<span class="card-name-interactive" data-card-name="${trimmedCardName}" role="button" tabindex="0">${trimmedCardName}</span>`;
    });
    return formattedText;
  };

  const renderFinalInterpretationView = () => {
    let titleText = `${userName}님의 운세`;
    if (selectedFortuneType === 'today') {
      titleText = `${userName}님의 오늘의 운세`;
    } else if (selectedFortuneType === 'year') {
      titleText = `${userName}님의 올해의 운세`;
    } else if (['customThreeCard', 'customFiveCard', 'customSevenCard', 'customTenCard'].includes(selectedFortuneType as string)) {
      titleText = `${userName}님의 고민에 대한 타로점`;
    }

    return (
      <div className="final-interpretation-container" style={{ maxWidth: '90%', margin: '0 auto', display: 'flex', flexDirection: 'column', height: '85vh' }}>
        <h3 className="text-2xl mb-2" style={{ fontWeight: '300', color: '#ffeb3b' }}>
          {titleText}
        </h3>
        
        {/* 선택된 카드들 미니 표시 - 최적화된 디자인 */}
        <div className="selected-cards-mini" style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          gap: '8px',
          marginBottom: '12px',
          flexShrink: 0 // 고정 높이 유지
        }}>
          {selectedCards.map(card => (
            <div key={card.id} className="mini-card-container" style={{ 
              width: '50px', // 더 작게
              position: 'relative'
            }}>
              <img 
                src={`/images/tarot/${card.imageName}`} 
                alt={card.name}
                style={{ 
                  width: '100%', 
                  height: 'auto', 
                  borderRadius: '5px',
                  boxShadow: '0 2px 6px rgba(0,0,0,0.25)',
                  transition: 'all 0.3s ease',
                  cursor: 'pointer'
                }}
                onClick={() => {
                  setModalCardDetails(card);
                  setShowCardDetailModal(true);
                }}
              />
            </div>
          ))}
        </div>
        
        {/* 최종 해석 텍스트 */}
        {isLoadingFortune && !finalInterpretationText && (
          <div className="loading-fortune text-center py-2 flex-grow" style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            <p className="text-lg mb-2">최종 해석을 불러오는 중...</p>
            <div className="loading-stars mt-2" style={{ display: 'flex', justifyContent: 'center', gap: '8px' }}>
              {[0.1, 0.3, 0.5].map(delay => (
                <span key={delay} style={{ animation: `mysticalShine 1.5s infinite ${delay}s`, display: 'inline-block' }}>
                  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" style={{ width: '24px', height: '24px', fill: '#ffeb3b' }}>
                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2L9.19 8.63L2 9.24l5.46 4.73L5.82 21L12 17.27z" />
                  </svg>
                </span>
              ))}
            </div>
          </div>
        )}
        
        {apiError && (
          <div className="error-message" style={{ color: 'red', marginBottom: '12px' }}>
            <p>오류: {apiError}</p>
          </div>
        )}
        
        {finalInterpretationText && (
          <div 
            className="interpretation-panel" 
            style={{ 
              textAlign: 'left', 
              padding: '12px 16px',
              background: 'rgba(30, 30, 60, 0.7)',
              borderRadius: '10px',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              flex: 1, // 남은 공간 채우기
              minHeight: 0, // Flexbox에서 올바르게 스크롤 작동하게 함
              overflowY: 'auto', 
              scrollbarWidth: 'thin', 
              scrollbarColor: '#ffeb3b rgba(30, 30, 60, 0.7)', 
              marginBottom: '12px' // 버튼과의 간격
            }}
            onClick={handleCardNameClick}
          >
            <p 
              className="result-card-interpretation" 
              style={{ 
                lineHeight: '1.5', 
                fontSize: '0.92rem',
                whiteSpace: 'pre-line',
                color: '#e0e0e0'
              }}
              dangerouslySetInnerHTML={{ __html: formatHighlightedText(finalInterpretationText) }}
            />
          </div>
        )}
        
        {/* 버튼 영역 */}
        {!isLoadingFortune && (
          <div className="action-buttons" style={{ marginTop: 'auto', flexShrink: 0 }}>
            <button 
              className="mystical-button" 
              onClick={handleStartOver}
              style={{ padding: '8px 16px', fontSize: '0.9rem' }}
            >
              처음으로 돌아가기
            </button>
          </div>
        )}
      </div>
    );
  };

  const renderResultEnd = () => {
    // Placeholder content for the result end full-screen view
    return (
      <>
        <h3>오늘의 운세 보기가 완료되었습니다.</h3>
        {finalInterpretationText && 
          <div>
            <h4>최종 요약:</h4>
            <p>{finalInterpretationText.substring(0, 100)}...</p> 
          </div>
        }
        <button onClick={handleStartOver} className="cta-button">다시 하기</button>
      </>
    );
  }

  const renderContent = () => {
    // This function now only needs to return the content specific to the view
    // The decision to render in full-screen or container is made in the main return
    switch (currentView) {
      case 'typeSelection':
        return (
          <div className="celestial-selection-container" style={{ width: '100%', animation: 'fadeIn 1s ease-out' }}>
            <div className="celestial-selection-header">
              <h2 className="celestial-title">운명의 두 갈래 길</h2>
              <p className="celestial-prompt">어떤 시간의 문을 여시겠습니까?</p>
            </div>
            <div className="destiny-choices-container">
              {/* Today's Fortune Orb */}
              <div 
                className="destiny-orb today-orb" 
                onClick={() => handleFortuneTypeSelect('today')}
                role="button"
                tabIndex={0}
                aria-label="오늘의 운세 보기"
              >
                <div className="orb-icon-container">
                  <svg className="orb-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="4.5" fill="#FFEB3B" stroke="none"/>
                    <path d="M12 2.5V1M12 23v-1.5M5.05 5.05L4 4M19.95 19.95l-1.05-1.05M2.5 12H1M23 12h-1.5M5.05 18.95L4 20M19.95 4l-1.05 1.05" stroke="#FFC107" />
                  </svg>
                </div>
                <h3 className="orb-title">오늘의 지혜</h3>
                <p className="orb-description">순간의 빛을 따라, 하루의 숨겨진 의미를 발견하세요.</p>
                <span className="orb-subtext">(무료 / 비로그인)</span>
              </div>

              {/* Year's Fortune Orb */}
              <div 
                className="destiny-orb year-orb" 
                onClick={() => handleFortuneTypeSelect('year')}
                role="button"
                tabIndex={0}
                aria-label="올해의 운세 보기"
              >
                <div className="orb-icon-container">
                  {/* Provided SVG for Year's Fortune - inline style removed */}
                  <svg className="orb-icon" viewBox="0 0 47.539 47.539" xmlSpace="preserve" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
                    <g>
                      <g>
                        <path d="M24.997,47.511C11.214,47.511,0,36.298,0,22.515C0,12.969,5.314,4.392,13.869,0.132 c0.385-0.191,0.848-0.117,1.151,0.186s0.381,0.766,0.192,1.15C13.651,4.64,12.86,8.05,12.86,11.601 c0,12.681,10.316,22.997,22.997,22.997c3.59,0,7.033-0.809,10.236-2.403c0.386-0.191,0.848-0.117,1.151,0.186 c0.304,0.303,0.381,0.766,0.192,1.15C43.196,42.153,34.597,47.511,24.997,47.511z M12.248,3.372C5.862,7.608,2,14.709,2,22.515 c0,12.68,10.316,22.996,22.997,22.996c7.854,0,14.981-3.898,19.207-10.343c-2.668,0.95-5.464,1.43-8.346,1.43 c-13.783,0-24.997-11.214-24.997-24.997C10.861,8.761,11.327,6.005,12.248,3.372z" />
                      </g>
                    </g>
                  </svg>
                </div>
                <h3 className="orb-title">한 해의 여정</h3>
                <p className="orb-description">별들의 지도를 펼쳐, 일 년의 깊은 통찰과 마주하세요.</p>
                <span className="orb-subtext">(로그인 및 {YEAR_FORTUNE_COST} 크레딧 필요)</span>
              </div>
            </div>
          </div>
        );
      case 'nameInput':
        return (
          <div className="name-input-section">
            <h3 className="section-title">
              {selectedFortuneType === 'today' ? '오늘의 운세를 위한 이름을 알려주세요' : 
               selectedFortuneType === 'year' ? '올해의 운세를 위한 이름을 알려주세요' :
               ['customThreeCard', 'customFiveCard', 'customSevenCard', 'customTenCard'].includes(selectedFortuneType as string) ? '타로점을 위한 이름을 알려주세요' :
               '운세를 위한 이름을 알려주세요'}
            </h3>
            <p className="section-subtitle">타로 카드는 당신의 에너지와 교감하며, 진실된 실명을 사용한다면 더 정확한 메시지를 전달받을 수 있습니다.<br />소중한 당신을 위한 별들의 메시지를 온전히 받아들이세요.</p>
            <input 
              type="text" 
              className="name-input"
              placeholder="진실된 마음으로 타로의 깊은 지혜와 만나보세요." 
              value={userName}
              onChange={(e) => setUserName(e.target.value)}
            />
            <button className="submit-name-btn" onClick={handleNameSubmit}>
              타로점 보기 준비!
            </button>
            <button className="back-btn" onClick={handleStartOver}> 
              다시 작성
            </button>
          </div>
        );
      case 'meditationIntro':
        return renderMeditationIntro();
      case 'cardCountSelection':
        return renderCardCountSelectionSection(); // Corrected this line
      case 'cardDrawing': // New or repurposed view for the actual card picking phase
        return renderFiveCardSelection();
      case 'cardRevealAndInterpretation':
        return renderCardReveal();
      case 'finalInterpretation':
        return renderFinalInterpretationView();
      case 'resultEnd':
         return renderResultEnd();
      case 'cooldownMessage':
      case 'loginRequiredMessage':
      case 'creditsRequiredMessage':
        let title = '';
        let message = cooldownMessageDetail; 
        let iconSvg = null;
        let cost = YEAR_FORTUNE_COST; // Default to year
        let fortuneName = '운세'; // Default

        if (['customThreeCard', 'customFiveCard', 'customSevenCard', 'customTenCard'].includes(selectedFortuneType as string)) {
          // For custom tarot, the cost depends on selectedCardCountForCustom, 
          // but at this stage, we might not have it. We can show a general message or use a default.
          // For now, let's use the cost of 3 cards as a placeholder or indicate variable cost.
          cost = CUSTOM_TAROT_COST_3_CARDS; // Use a defined constant
          fortuneName = '고민 해결 타로';
        } else if (selectedFortuneType === 'today') {
          // Today's might be free, or have a different cost/name
          // For now, let's assume it implies free if not custom or year
          fortuneName = '오늘의 운세';
          cost = 0; // Example: today's is free
        }

        if (currentView === 'loginRequiredMessage') {
          title = '🔒 로그인이 필요해요!';
          message = `해당 ${fortuneName}은(는) 로그인 후 이용 가능합니다.\n상단의 로그인 버튼을 이용해주세요.`;
          iconSvg = (
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C9.243 2 7 4.243 7 7v3H6a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2v-8a2 2 0 00-2-2h-1V7c0-2.757-2.243-5-5-5zm0 2c1.654 0 3 1.346 3 3v3H9V7c0-1.654 1.346-3 3-3zm-4 8h10v6H8v-6z"/>
            </svg>
          );
        } else if (currentView === 'creditsRequiredMessage') {
          title = '✨ 크레딧이 부족해요!';
          message = `${fortuneName}을(를) 보려면 ${cost} 크레딧이 필요합니다.\n현재 ${user?.credits || 0} 크레딧 보유중입니다.\n\n크레딧을 충전하거나 획득한 후 다시 시도해주세요.`;
          iconSvg = (
            <img src="/images/credit.png" alt="크레딧 부족" style={{
              width: '70px',
              height: '70px',
              filter: 'drop-shadow(0 0 8px rgba(255, 215, 0, 0.5))'
            }} />
          );
        } else { // Cooldown case
          title = '🕰️ 잠시 후 다시 오세요'; // Default cooldown title if not overridden by specific message
          // message is already set to cooldownMessageDetail which is now just the remaining time string.
          // if message is empty (e.g. cooldownMessageDetail wasn't set for some reason)
          if (!message) message = "운세를 다시 보려면 약간의 시간이 필요합니다.";
          iconSvg = (
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
            </svg>
          );
        }
        return (
          <div className="mystical-feedback-container">
            <div className="mystical-feedback-portal">
              <div className="feedback-icon-area">
                {iconSvg}
              </div>
              <h3 className="feedback-portal-title">{title}</h3>
              <p className="feedback-portal-message">{message}</p>
              <button className="feedback-portal-button mystical-button" onClick={handleDirectReturnToTypeSelection}> 
                돌아가기
              </button>
            </div>
          </div>
        );
      case 'concernInput':
        return renderConcernInputSection();
      default:
        // const exhaustiveCheck: never = currentView; // This will cause error if new views are not handled
        // To fix the "is not assignable to type 'never'" error, handle all View types explicitly or remove/comment out exhaustiveCheck
        if (currentView === 'cardDrawing') { // Example of handling another case
            return <p>카드 뽑기 화면 준비 중...</p>; 
        }
        return <p>알 수 없는 상태입니다: {currentView}. <button onClick={handleStartOver}>처음으로 돌아가기</button></p>;
    }
  };

  const renderSoundControls = () => {
    // Calculate volume percentage for display
    const volumePercentage = Math.round(soundVolume * 100);
    
    return (
      <div className="mystical-volume-controls" style={{ 
        position: 'fixed', 
        top: '20px', 
        right: '20px', 
        zIndex: 10000,
        background: 'rgba(20, 10, 40, 0.75)',
        backdropFilter: 'blur(8px)',
        padding: '12px 15px',
        borderRadius: '12px',
        border: '1px solid rgba(255, 235, 59, 0.3)',
        boxShadow: '0 4px 15px rgba(0, 0, 0, 0.4), 0 0 10px rgba(255, 215, 59, 0.2)',
        color: 'white',
        display: isFullScreenView ? 'flex' : 'none',
        alignItems: 'center',
        gap: '10px',
        transition: 'all 0.3s ease',
        animation: 'fadeIn 0.5s ease-out'
      }}>
        <button 
          onClick={() => setIsSoundMuted(prev => !prev)} 
          className={!isSoundMuted ? "sound-icon-active" : ""}
          style={{ 
            background: 'transparent',
            border: 'none',
            fontSize: '1.5rem',
            cursor: 'pointer',
            color: isSoundMuted ? '#888' : '#ffeb3b',
            textShadow: isSoundMuted ? 'none' : '0 0 10px rgba(255, 235, 59, 0.6)',
            transition: 'all 0.2s ease',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '30px',
            height: '30px',
            borderRadius: '50%'
          }}
          aria-label={isSoundMuted ? '음소거 해제' : '음소거'}
          title={isSoundMuted ? '음소거 해제' : '음소거'}
        >
          {isSoundMuted ? '🔇' : '🔊'}
        </button>
        
        <div style={{
          position: 'relative',
          width: '80px',
          height: '20px',
        }}>
          {/* Custom styled track and filled portion (visible elements) */}
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '0',
            right: '0',
            height: '6px',
            transform: 'translateY(-50%)',
            background: 'rgba(255, 255, 255, 0.2)',
            borderRadius: '3px',
            overflow: 'hidden'
          }}>
            <div style={{
              position: 'absolute',
              top: '0',
              left: '0',
              height: '100%',
              width: `${soundVolume * 100}%`,
              background: isSoundMuted ? 
                'rgba(150, 150, 150, 0.4)' : 
                'linear-gradient(90deg, #ffeb3b, #ff9800)',
              borderRadius: '3px',
              transition: 'width 0.1s ease, background 0.3s ease'
            }}/>
          </div>
          
          {/* Volume percentage tooltip */}
          <div className="mystical-volume-tooltip" style={{
            position: 'absolute',
            bottom: '-24px',
            left: `calc(${soundVolume * 100}% - 15px)`, // Center under thumb
            background: 'rgba(0, 0, 0, 0.7)',
            color: isSoundMuted ? '#888' : '#ffeb3b',
            fontSize: '10px',
            padding: '2px 5px',
            borderRadius: '3px',
            transition: 'left 0.1s ease',
            whiteSpace: 'nowrap'
          }}>
            {isSoundMuted ? '음소거' : `${volumePercentage}%`}
          </div>
          
          {/* Actual range input - using browser styling from CSS */}
          <input 
            type="range" 
            min="0" 
            max="1" 
            step="0.01" 
            value={soundVolume}
            onChange={(e) => setSoundVolume(parseFloat(e.target.value))} 
            disabled={isSoundMuted}
            style={{ 
              position: 'absolute',
              top: '0',
              left: '0',
              width: '100%',
              height: '100%',
              margin: '0',
              opacity: '0', // Hidden but functional
              cursor: 'pointer'
            }}
            aria-label="볼륨 조절"
            title={`볼륨: ${volumePercentage}%`}
          />
          
          {/* Custom thumb (visible element) */}
          <div style={{
            position: 'absolute',
            top: '50%',
            left: `calc(${soundVolume * 100}% - 7px)`, // Center the thumb
            width: '14px',
            height: '14px',
            borderRadius: '50%',
            background: isSoundMuted ? '#888' : '#ffeb3b',
            boxShadow: isSoundMuted ? 'none' : '0 0 8px rgba(255, 235, 59, 0.7)',
            transform: 'translateY(-50%)',
            transition: 'left 0.1s ease, background 0.3s ease',
            pointerEvents: 'none' // So it doesn't interfere with the slider
          }}/>
        </div>
      </div>
    );
  }

  // Define which views should cover the full screen
  const fullScreenViews: View[] = [
    'concernInput', // 이제 고민 입력도 풀스크린
    'nameInput', // 이름 입력도 풀스크린으로 변경
    'meditationIntro',
    'cardCountSelection',
    'cardDrawing', // Added this line
    'cardRevealAndInterpretation',
    'finalInterpretation',
    'resultEnd',
    'cooldownMessage',
    'loginRequiredMessage',
    'creditsRequiredMessage'
  ];

  const isFullScreenView = fullScreenViews.includes(currentView);

  const handleCardClick = (card: TarotCard) => {
    const isAlreadySelected = selectedCards.some(sc => sc.id === card.id);
    // const currentNumCardsToSelect = selectedFortuneType === 'year' ? NUM_CARDS_FIVE_SPREAD : NUM_CARDS_THREE_SPREAD; // Old way
    // currentNumCardsToSelect is now a state

    if (selectedCards.length < currentNumCardsToSelect && !isAlreadySelected) {
      // 카드 선택 효과음 재생
      playSound(SOUND_PATHS.cardPlace);
      
      setSelectedCards([...selectedCards, card]);
      if (selectedCards.length + 1 === currentNumCardsToSelect) {
        setShowCardSelectionButton(true);
      }
    } else if (isAlreadySelected) {
      // 카드 선택 해제 효과음 재생
      playSound(SOUND_PATHS.cardPlaceCancel);
      
      setSelectedCards(selectedCards.filter(sc => sc.id !== card.id));
      setShowCardSelectionButton(false); // Always hide if a card is deselected
    }
  };

  const handleConfirmCards = () => {
    // const currentNumCardsToSelect = selectedFortuneType === 'year' ? NUM_CARDS_FIVE_SPREAD : NUM_CARDS_THREE_SPREAD; // Old way
    // currentNumCardsToSelect is now a state
    if (selectedCards.length === currentNumCardsToSelect) {
      // playSound(SOUND_PATHS.cardShuffle);
      addChatMessage("선택하신 카드를 하나씩 공개하겠습니다. 각 카드가 당신에게 전하는 메시지에 귀를 기울여보세요.", 'system');
      
      // 애니메이션과 함께 카드 공개 화면으로 이동
      transitionToView('cardRevealAndInterpretation');
      setCurrentCardRevealStep(0); // 첫 번째 카드부터 공개 시작
      setRevealedCardsInfo([]); // 이전에 공개된 카드 정보 초기화
    }
  };

  // 개선된 장면 전환 함수
  const transitionToView = (nextView: View) => {
    // 초기 유형 선택화면으로 돌아갈 때는 배경 전체를 덮는 오버레이 숨기기
    const isReturningToNonFullscreen = 
      fullScreenViews.includes(currentView) && 
      !fullScreenViews.includes(nextView);

    // 운세 음악 재생 또는 중지
    const fortuneViews = ['meditationIntro', 'cardCountSelection', 'cardRevealAndInterpretation', 'finalInterpretation', 'resultEnd'];
    
    // 운세 관련 화면에서 다른 화면으로 이동하는 경우에만 음악 중지
    if (!fortuneViews.includes(nextView) && fortuneViews.includes(currentView)) {
      stopSound();
    } 
    // 운세 관련 화면으로 처음 진입할 때만 음악 재생 (이미 재생 중이면 중복해서 재생하지 않음)
    else if (fortuneViews.includes(nextView) && !isFortuneMusic) {
      playSound(SOUND_PATHS.fortuneMusic);
    }

    // 1. 컨텐츠 페이드 아웃
    setContentOpacity(0);
    
    // 2. 오버레이 표시
    setShowTransitionOverlay(true);
    
    // 3. 페이드 아웃 완료 후 뷰 변경
    setTimeout(() => {
      setCurrentView(nextView);
      
      // 4. 카드 공개 화면 특수 처리
      if (nextView === 'cardRevealAndInterpretation') {
        // Initialize flip states when the view is set
        const cardsInSpread = currentNumCardsToSelect; // Use currentNumCardsToSelect state
        setCardFlipStates(new Array(cardsInSpread).fill(false)); 
      }
      
      // 5. 뷰 변경 후 약간의 지연을 두고 컨텐츠 페이드 인
      setTimeout(() => {
        setContentOpacity(1);
        
        // 6. 카드 공개 화면이면 자동 카드 공개 시작
        if (currentView === 'cardRevealAndInterpretation') {
          setTimeout(() => {
            setAutoRevealActive(true);
          }, 1000);
        }
        
        // 7. 컨텐츠가 표시된 후 오버레이 숨기기
        setTimeout(() => {
          setShowTransitionOverlay(false);
        }, 800);
      }, 400);
    }, 800);
  };

  // 새로운: 백엔드에 최종 운세 해석 요청
  const fetchFinalInterpretation = async () => {
    // 중복 호출 방지 - ref를 사용하여 즉시 체크
    if (isRequestingFinalInterpretation.current) {
      console.log("[Tarot] Final interpretation request already in progress, skipping duplicate call");
      return;
    }

    // Check if there are any selected cards first
    if (!selectedCards || selectedCards.length === 0) {
      console.log("[Tarot] No cards selected, cancelling interpretation request");
      return;
    }
    
    // Ensure we have the right number of cards for the selected fortune type
    let requiredCardCount = selectedCards.length; // 기본값: 실제 선택된 카드 수 (매니저 스프레드용)
    
    // 기존 고정 타입들에 대해서만 특정 카드 수 요구
    if (selectedFortuneType === 'customThreeCard') {
      requiredCardCount = 3;
    } else if (selectedFortuneType === 'customFiveCard') {
      requiredCardCount = 5;
    } else if (selectedFortuneType === 'customSevenCard') {
      requiredCardCount = 7;
    } else if (selectedFortuneType === 'customTenCard') {
      requiredCardCount = 10;
    } else if (selectedFortuneType === 'year') {
      requiredCardCount = 5;
    } else if (selectedFortuneType === 'today') {
      requiredCardCount = 3;
    }
    // 매니저 스프레드 (custom6, custom4, 등)는 실제 선택된 카드 수 사용
    
    if (selectedCards.length !== requiredCardCount) {
      console.log("[Tarot] Card count mismatch, cancelling interpretation request");
      return;
    }
    
    // 이미 해석이 완료된 경우 중복 요청 방지
    if (finalInterpretationText) {
      console.log("[Tarot] Final interpretation already exists, cancelling request");
      return;
    }
    
    // 이미 API 요청 중이면 중복 요청 방지
    if (isLoadingFortune) {
      console.log("[Tarot] Already loading fortune, cancelling duplicate request");
      return;
    }
    
    console.log("[Tarot] Starting fortune interpretation request");
    // API 요청 시작 플래그 설정
    isRequestingFinalInterpretation.current = true;
    setIsLoadingFortune(true);
    
    // 이미 최종 해석 준비 중 메시지가 표시되어 있지 않다면 표시
    if (!showFinalInterpretationLoading) {
      setShowFinalInterpretationLoading(true);
    }
    
    addChatMessage('선택하신 모든 카드를 바탕으로 AI 요정이 전체적인 해석을 준비하고 있어요. 잠시만 기다려주세요...', 'system');

    // Determine fortuneType for API based on selectedFortuneType
    let apiFortuneType = 'todayThreeCard'; // Default
    if (selectedFortuneType === 'year') {
      apiFortuneType = 'yearFiveCard';
    } else if (['customThreeCard', 'customFiveCard', 'customSevenCard', 'customTenCard'].includes(selectedFortuneType as string)) {
      // Map the frontend type to the backend type if they differ, or use directly if they match
      // Assuming backend expects 'customThreeCard', 'customFiveCard', etc.
      apiFortuneType = selectedFortuneType as string; 
    } else if (selectedFortuneType && selectedFortuneType.startsWith('custom') && selectedSpreadId) {
      // 매니저 스프레드인 경우 - spreadId가 있으면 매니저 스프레드로 처리
      apiFortuneType = selectedFortuneType as string;
    }

    // requestId를 생성하여 각 요청을 식별할 수 있게 함
    const requestId = `fortune-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    console.log(`[Tarot] 타로 해석 요청 시작 (${requestId})`);

    const payload = {
      userName,
      userConcern: (['customThreeCard', 'customFiveCard', 'customSevenCard', 'customTenCard'].includes(selectedFortuneType as string) || (selectedFortuneType && selectedFortuneType.startsWith('custom') && selectedSpreadId)) ? userConcern : undefined,
      selectedCards: selectedCards.map(c => ({ id: c.id, name: c.name, description: c.description, imageName: c.imageName })),
      fortuneType: apiFortuneType,
      requestId: requestId, // 요청 식별자 추가
      spreadId: selectedSpreadId, // 선택된 스프레드 ID 추가
    };

    const headers: HeadersInit = { 
      'Content-Type': 'application/json',
      'X-Request-ID': requestId, // 요청 식별자 헤더 추가
    };
    
    if (token && (apiFortuneType === 'yearFiveCard' || 
                 apiFortuneType === 'customThreeCard' || 
                 apiFortuneType === 'customFiveCard' || 
                 apiFortuneType === 'customSevenCard' || 
                 apiFortuneType === 'customTenCard' ||
                 (apiFortuneType && apiFortuneType.startsWith('custom') && selectedSpreadId))) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    try {
      // 타임아웃을 위한 AbortController 생성
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30초 타임아웃

      const response = await fetch(`/api/fortune/generate?_=${requestId}`, { // 캐시 방지 파라미터 추가
        method: 'POST',
        headers: headers,
        body: JSON.stringify(payload),
        cache: 'no-store', // 캐시를 사용하지 않도록 설정
        signal: controller.signal, // AbortController 신호 추가
      });

      // 요청 성공 시 타임아웃 클리어
      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json();
        console.error(`[Tarot] API error (${requestId}):`, errorData);
        throw { ...errorData, status: response.status };
      }

      const data = await response.json();
      console.log(`[Tarot] 타로 해석 응답 받음 (${requestId})`);
      const interpretation = data.interpretation;
      
      if (!interpretation) {
        throw { error: '타로 해석 결과를 받아오지 못했습니다.' };
      }

      setFinalInterpretationText(interpretation);
      setFortuneInterpretation(interpretation);
      addChatMessage(interpretation, 'ai');
      setIsLoadingFortune(false);
      // API 요청 완료 플래그 해제
      isRequestingFinalInterpretation.current = false;
      
      // 최종 해석 준비 중 메시지 표시는 아직 유지 - 클라이언트 측에서 명시적으로 화면 전환될 때까지 유지
      
      // 모든 카드 소개가 완료된 상태인지 확인하고, 완료된 경우 finalInterpretation 화면으로 전환
      const allCardsRevealed = cardFlipStates.every(state => state);
      if (allCardsRevealed) {
      setTimeout(() => {
          setShowFinalInterpretationLoading(false); // 화면 전환 직전에 로딩 표시 제거
        transitionToView('finalInterpretation');
      }, 2000);
      } else {
        // 아직 카드 소개 중이면 버튼 표시하여 사용자가 결과 볼 수 있도록 안내
        setTimeout(() => {
          if (currentView === 'cardRevealAndInterpretation') {
            addChatMessage('모든 카드의 해석이 준비되었습니다. 최종 해석으로 이동하시겠어요?', 'system');
          }
        }, 2000);
        // 아직 showFinalInterpretationLoading은 유지 - 모든 카드 소개 끝날 때까지 보여야 함
      }
    } catch (errorData: any) {
      console.error('Error fetching final interpretation:', errorData);
      let errorMessage = errorData.error || '최종 운세 정보를 가져오는데 실패했습니다.';
      
      // 타임아웃 에러 처리
      if (errorData.name === 'AbortError') {
        console.log("[Tarot] Request timeout - returning to spread selection");
        
        // 타임아웃 발생 시 사용자에게 알리고 스프레드 선택 페이지로 돌아가기
        addChatMessage('요청 처리 시간이 너무 오래 걸려 중단되었습니다. 다시 시도해주세요.', 'system');
        
        // 상태 초기화
        setShowFinalInterpretationLoading(false);
        setIsLoadingFortune(false);
        isRequestingFinalInterpretation.current = false;
        
        // 1.5초 후 스프레드 선택 페이지로 돌아가기
        setTimeout(() => {
          // 관련 상태들 초기화
          setSelectedCards([]);
          setFinalInterpretationText(null);
          setFortuneInterpretation(null);
          setCurrentRevealingCard(null);
          setIsIntroducingCard(false);
          setCurrentView('cardCountSelection');
        }, 1500);
        
        return;
      }
      
      // 중복 요청 에러일 경우(potential_duplicate_request), 메시지를 숨기고 계속 대기
      if (errorData.reason === 'potential_duplicate_request') {
        console.log("[Tarot] 중복 요청이 감지되었습니다. 이전 요청 결과를 기다립니다.");
        // 오류 메시지를 표시하지 않고, 로딩 상태 유지
        // 이 경우에는 첫 번째 요청이 성공하면 그 결과가 표시될 것입니다
        setIsLoadingFortune(false);
        isRequestingFinalInterpretation.current = false;
        return;
      }
      
      // Only show cooldown message if we're not already in card revealing view
      // This prevents interrupting the user experience with a cooldown message
      if (currentView !== 'cardRevealAndInterpretation' || 
          (currentCardRevealStep === 0 && !isIntroducingCard)) {
      if (errorData.status === 429) {
        const { hours = 0, minutes = 0 } = errorData.remainingTime || {};
        errorMessage = `이미 해당 운세를 보셨습니다. 약 ${hours}시간 ${minutes}분 후에 다시 시도해주세요.`;
        setCooldownMessageDetail(errorMessage);
        setCurrentView('cooldownMessage');
      } else if (errorData.status === 401) {
        setCurrentView('loginRequiredMessage');
      } else if (errorData.status === 402) {
        setCurrentView('creditsRequiredMessage');
        } else {
        setApiError(errorMessage);
        addChatMessage(errorMessage + ' 스프레드 선택으로 돌아가시려면 잠시 후 자동으로 이동됩니다.', 'system');
        
        // 일반 에러 발생 시에도 5초 후 스프레드 선택 페이지로 돌아가기
        setTimeout(() => {
          setSelectedCards([]);
          setFinalInterpretationText(null);
          setFortuneInterpretation(null);
          setCurrentRevealingCard(null);
          setIsIntroducingCard(false);
          setCurrentView('cardCountSelection');
        }, 5000);
        }
      } else {
        // If we're already in card reveal, just add a message instead of showing modal
        setApiError(errorMessage);
        addChatMessage('최종 해석을 가져오는 중 오류가 발생했습니다. 5초 후 스프레드 선택으로 돌아갑니다.', 'system');
        
        // 카드 공개 중 에러 발생 시에도 5초 후 스프레드 선택 페이지로 돌아가기
        setTimeout(() => {
          setSelectedCards([]);
          setFinalInterpretationText(null);
          setFortuneInterpretation(null);
          setCurrentRevealingCard(null);
          setIsIntroducingCard(false);
          setCurrentView('cardCountSelection');
        }, 5000);
      }
      
      // 오류 시에도 최종 해석 준비 중 메시지 표시 중지
      setShowFinalInterpretationLoading(false);
      setIsLoadingFortune(false);
      // API 요청 완료 플래그 해제
      isRequestingFinalInterpretation.current = false;
    }
  };

  // useEffect 추가: 카드 공개 화면으로 전환될 때 초기 상태 설정
  useEffect(() => {
    if (currentView === 'cardRevealAndInterpretation') {
      // 화면 전환 시 초기 상태 명확히 설정
      setCardsInStartPosition(true);
      setCardsInitializing(true);
      setFanAnimationComplete(false);
      setCardsMovingToPosition(false);
      setCurrentMovingCardIndex(null);
      setCardFlipStates(new Array(selectedCards.length).fill(false));
      // Initialize cardZIndices for the spread: e.g., 1000, 1001, 1002...
      setCardZIndices(selectedCards.map((_, i) => baseZIndex + i)); 
      setCurrentRevealingCard(null);
      setHighlightedCardIndex(null);
      setShowFinalInterpretationLoading(false);
      
      // 카드 뷰가 로드된 후 약간의 지연 시간을 두고 자동 카드 애니메이션 시작
      const timer: ReturnType<typeof setTimeout> = setTimeout(() => {
        // 자동 카드 공개를 활성화
        setAutoRevealActive(true);
      }, 1200);

      return () => clearTimeout(timer); // 컴포넌트 언마운트나 뷰 변경 시 타이머 정리
    } else {
      // 카드 공개 화면을 떠날 때는 autoRevealActive 비활성화
      setAutoRevealActive(false);
    }
  }, [currentView, selectedCards.length]); // Ensure selectedCards.length is a dependency

  // 자동 카드 공개 효과 - 기존 useEffect 수정
  useEffect(() => {
    // 이제 카드 공개 로직은 moveNextCard와 handleCardIntroTypingComplete에서 처리됩니다.
    if (autoRevealActive && currentView === 'cardRevealAndInterpretation' && !isIntroducingCard && !cardsMovingToPosition) {
      // 애니메이션이 시작되지 않았을 때만 첫 번째 카드 이동 시작
      if (cardsInStartPosition && !currentRevealingCard && fanAnimationComplete === false) {
        startCardAnimationSequence();
      }
    }
    
    // 컴포넌트 언마운트 시 타이머 정리
    return () => {
      if (revealTimer) {
      clearTimeout(revealTimer);
      }
    };
  }, [autoRevealActive, currentView, isIntroducingCard, cardsMovingToPosition, cardsInStartPosition, currentRevealingCard, fanAnimationComplete]);

  // revealTimer 변수 추가
  const [revealTimer, setRevealTimer] = useState<ReturnType<typeof setTimeout> | null>(null);

  // 중복 API 호출 방지를 위한 ref
  const isRequestingFinalInterpretation = useRef(false);

  // 카드 소개 타이핑 완료 후 처리
  const handleCardIntroTypingComplete = () => {
    // 카드 소개가 끝나면 먼저 하이라이트 효과 제거
    setHighlightedCardIndex(null);

    // 현재 카드의 소개가 끝난 후 다음 카드로 진행
    setTimeout(() => {
      setIsIntroducingCard(false); // 소개 종료
      
      // 현재 카드의 인덱스 확인 (currentRevealingCard 사용)
      if (currentRevealingCard !== null) {
        const nextIndex = currentRevealingCard + 1;
        
        // 다음 카드가 있으면 다음 카드로 진행
        if (nextIndex < selectedCards.length) {
          setTimeout(() => {
            moveNextCard(nextIndex);
          }, 500); // 다음 카드 이동까지 약간의 딜레이
        } else {
          // 마지막 카드 소개까지 완전히 끝난 후에 최종 해석 준비 메시지 표시
          setTimeout(() => {
            // 이미 최종 해석이 진행 중이거나 완료되었으면 건너뛰기
            if (isLoadingFortune || finalInterpretationText) {
              console.log("[Tarot] Skipped final interpretation request - already in progress or completed");
              if (finalInterpretationText) {
                setTimeout(() => {
                  setShowFinalInterpretationLoading(false);
                  transitionToView('finalInterpretation');
                }, 2000);
              }
              return;
            }

            // 최종 해석 준비 중 메시지를 표시
            setShowFinalInterpretationLoading(true);
            
            // API 호출을 1초 후에 실행
            const apiCallTimerId = setTimeout(() => {
              // 최종 호출 직전에 다시 한번 확인
              if (!isLoadingFortune && !finalInterpretationText) {
                console.log("[Tarot] Requesting final interpretation");
                fetchFinalInterpretation();
              } else {
                console.log("[Tarot] Prevented duplicate final interpretation request", {
                  isLoadingFortune,
                  finalInterpretationText: !!finalInterpretationText
                });
              }
            }, 1000);
            
            // 컴포넌트 언마운트 시 타이머 제거를 위해 상태 저장
            setRevealTimer(apiCallTimerId as unknown as ReturnType<typeof setTimeout>);
          }, 1000);
        }
      }
    }, 2000); // 타이핑 완료 후 2초 대기
  };

  // 개별 카드 소개 텍스트 생성 함수
  const generateCardIntroductionText = (card: TarotCard, index: number): string => {
    // 먼저 현재 스프레드에 해당하는 카드 소개 템플릿이 있는지 확인
    const currentSpreadType = selectedFortuneType === 'year' ? 'yearFiveCard' : selectedFortuneType;
    
    // 활성 스프레드 데이터에서 현재 스프레드 찾기
    const currentSpread = activeSpreads?.find(spread => spread.spreadType === currentSpreadType);
    
    if (currentSpread && currentSpread.cardIntroTemplates) {
      try {
        const templates = JSON.parse(currentSpread.cardIntroTemplates);
        if (Array.isArray(templates) && templates[index]) {
          // 템플릿에서 변수 치환
          let introText = templates[index];
          introText = introText.replace(/{userName}/g, userName || '사용자');
          introText = introText.replace(/{cardName}/g, card.name);
          introText = introText.replace(/{cardIndex}/g, (index + 1).toString());
          introText = introText.replace(/{totalCards}/g, selectedCards.length.toString());
          
          // 이스케이프된 줄바꿈을 실제 줄바꿈으로 변환
          introText = introText.replace(/\\n/g, '\n');
          
          return introText;
        }
      } catch (error) {
        console.warn('카드 소개 템플릿 파싱 오류:', error);
      }
    }

    // 템플릿이 없으면 기존 하드코딩된 로직 사용 (fallback)
    // 운세 유형 확인
    const isYearFortune = selectedFortuneType === 'year';
    const isCustomThreeCard = selectedFortuneType === 'customThreeCard';
    const isCustomFiveCard = selectedFortuneType === 'customFiveCard';
    const isCustomSevenCard = selectedFortuneType === 'customSevenCard';
    const isCustomTenCard = selectedFortuneType === 'customTenCard';
    const isTodayFortune = selectedFortuneType === 'today';

    // 기본 텍스트 포맷 (카드 이름 표시 일관성)
    const cardName = `**${card.name}**`;
    
    // 연간 운세 (5카드 스프레드)
    if (isYearFortune) {
      const yearPrompts = [
        `첫 번째 카드, ${cardName}입니다.\n\n이 카드는 ${userName}님 한 해의 가장 근원적인 '씨앗' 혹은 시작될 새로운 테마를 상징합니다.\n\n어떤 이야기가 담겨 있을까요?`, // 씨앗
        `두 번째 카드, ${cardName}입니다.\n\n이 카드는 ${userName}님이 올해 걸어갈 '펼쳐지는 길' 위에서 마주할 주요 과정과 발전을 보여줍니다.\n\n이 여정에서 어떤 성장이 기다리고 있을까요?`, // 펼쳐지는 길
        `세 번째 카드, ${cardName}입니다.\n\n이 카드는 ${userName}님 한 해의 중요한 '운명의 전환점' 혹은 핵심적인 도전을 드러냅니다.\n\n어떤 중요한 선택이나 변화가 찾아올지 살펴보세요.`, // 운명의 전환점
        `네 번째 카드, ${cardName}입니다.\n\n이 카드는 여정 속에서 ${userName}님이 발견할 '숨겨진 선물' 또는 귀중한 교훈을 암시합니다.\n\n어떤 예상치 못한 행운이 기다리고 있을까요?`, // 숨겨진 선물과 교훈
        `마지막 다섯 번째 카드, ${cardName}입니다.\n\n이 카드는 ${userName}님의 한 해를 궁극적으로 인도할 '지혜의 별'이자, 다가올 시간에 대한 최종적인 지침을 담고 있습니다.\n\n이 카드의 메시지를 마음에 새겨두세요.` // 인도하는 별
      ];
      return yearPrompts[index] || `올해의 ${index + 1}번째 카드, ${cardName}입니다. 이 카드는 어떤 의미를 가지고 있을까요?`;
    }
    
    // 오늘의 운세 (3카드 스프레드)
    else if (isTodayFortune) {
      const todayPrompts = [
        `첫 번째 카드는 ${cardName}입니다.\n\n이 카드는 오늘 ${userName}님이 마주할 **현재 상황**을 보여줍니다.\n\n어떤 에너지가 당신을 감싸고 있을까요?`, // 상황
        `두 번째 카드는 ${cardName}입니다.\n\n이 카드는 오늘 ${userName}님에게 주어진 **과제** 혹은 집중해야 할 부분을 나타냅니다.\n\n이 메시지를 하루 동안 마음에 새겨두세요.`, // 과제
        `마지막 세 번째 카드는 ${cardName}입니다.\n\n이 카드는 오늘의 흐름이 어떤 **결과**로 이어질 수 있는지 그 가능성을 보여줍니다.\n\n이 방향으로 나아가면 어떤 경험이 기다리고 있을까요?` // 결과
      ];
      return todayPrompts[index] || `오늘의 ${index + 1}번째 카드, ${cardName}입니다. 이 카드는 어떤 의미를 가지고 있을까요?`;
    }
    
          // 고민 기반 3카드 스프레드 (과거-현재-미래 또는 상황-장애물-조언)
    else if (isCustomThreeCard) {
      const threeCardPrompts = [
        `첫 번째 카드, ${cardName}입니다.\n\n이 카드는 당신의 질문과 관련된 **근본적인 상황** 또는 **현재의 에너지**를 나타냅니다.\n\n무엇이 당신의 상황을 이끌고 있는지 살펴보세요.`, // 상황/현재
        `두 번째 카드, ${cardName}입니다.\n\n이 카드는 고민을 해결하기 위한 **도전 과제** 또는 당신이 **고려해야 할 중요한 요소**를 제시합니다.\n\n이 카드가 보여주는, 뚫고 지나가야 할 장애물에 주목하세요.`, // 장애물/고려사항
        `마지막 카드, ${cardName}입니다.\n\n이 카드는 당신의 질문에 대한 **결과적인 조언**과 앞으로 나아갈 방향을 암시합니다.\n\n어떤 행동이 가장 지혜로울지 생각해보세요.` // 조언/방향
      ];
      return threeCardPrompts[index] || `당신의 고민에 대한 ${index + 1}번째 카드, ${cardName}입니다. 이 카드는 어떤 메시지를 전달할까요?`;
    } 
    
          // 고민 기반 5카드 스프레드 (십자가 또는 W 형태)
    else if (isCustomFiveCard) {
      // 십자가 또는 W 패턴에 따른 카드 위치의 의미
      const fiveCardPrompts = [
        `첫 번째 카드, ${cardName}입니다.\n\n이 카드는 당신의 고민에 대한 **현재 상황의 핵심**을 보여줍니다.\n\n지금 이 순간 가장 중요한 에너지가 무엇인지 살펴보세요.`, // 중앙/현재
        `두 번째 카드, ${cardName}입니다.\n\n이 카드는 당신 앞에 놓인 **장애물**이나 **도전**을 상징합니다.\n\n어떤 어려움이 해결을 가로막고 있는지 확인하세요.`, // 장애물
        `세 번째 카드, ${cardName}입니다.\n\n이 카드는 당신의 상황에 **숨겨진 영향력**이나 **인식하지 못한 요소**를 드러냅니다.\n\n아직 알지 못했던 중요한 통찰을 제공합니다.`, // 무의식/숨겨진 영향
        `네 번째 카드, ${cardName}입니다.\n\n이 카드는 질문에 대한 **구체적인 조언**과 당신이 **취해야 할 행동**을 보여줍니다.\n\n어떤 접근 방식이 가장 도움이 될지 고려해 보세요.`, // 조언/행동
        `다섯 번째 카드, ${cardName}입니다.\n\n이 카드는 현재 경로를 따랐을 때의 **잠재적 결과**와 **미래의 가능성**을 암시합니다.\n\n이것이 당신의 여정의 목적지가 될 수 있습니다.` // 결과/미래
      ];
      return fiveCardPrompts[index] || `당신의 고민에 대한 ${index + 1}번째 카드, ${cardName}입니다. 이 카드는 어떤 메시지를 전달할까요?`;
    }
    
          // 고민 기반 7카드 스프레드 (말발굽 또는 영적 여정)
    else if (isCustomSevenCard) {
      const sevenCardPrompts = [
        `첫 번째 카드, ${cardName}입니다.\n\n이 카드는 당신의 질문에 관한 **현재 상황**과 **출발점**을 보여줍니다.\n\n지금 당신이 서 있는 위치를 명확히 인식하세요.`, // 현재 상황
        `두 번째 카드, ${cardName}입니다.\n\n이 카드는 당신 앞에 놓인 **즉각적인 도전**이나 **해결해야 할 문제**를 나타냅니다.\n\n어떤 장애물이 가장 먼저 나타나는지 확인하세요.`, // 즉각적 도전
        `세 번째 카드, ${cardName}입니다.\n\n이 카드는 상황에 영향을 미치는 **과거의 기반**이나 **근본 원인**을 보여줍니다.\n\n어떤 패턴이 현재까지 이어져 왔는지 살펴보세요.`, // 과거/근본 원인
        `네 번째 카드, ${cardName}입니다.\n\n이 카드는 이 상황에서 당신이 **점차 떠나보내야 할 것**과 **변화가 필요한 부분**을 알려줍니다.\n\n무엇을 내려놓아야 성장할 수 있을까요?`, // 떠나보내야 할 것
        `다섯 번째 카드, ${cardName}입니다.\n\n이 카드는 당신에게 **도움이 될 수 있는 영향력**과 **활용해야 할 자원**을 나타냅니다.\n\n어떤 지원이 당신을 기다리고 있는지 인식하세요.`, // 도움/자원
        `여섯 번째 카드, ${cardName}입니다.\n\n이 카드는 앞으로 나아가기 위한 **구체적인 조언**과 **취해야 할 행동**을 보여줍니다.\n\n이 지혜를 어떻게 실천할 수 있을까요?`, // 조언/행동
        `일곱 번째 카드, ${cardName}입니다.\n\n이 카드는 이 여정의 **최종 결과**와 **잠재적 미래**를 암시합니다.\n\n이 길을 따랐을 때 어떤 가능성이 당신을 기다리고 있을까요?` // 결과/미래
      ];
      return sevenCardPrompts[index] || `당신의 고민에 대한 ${index + 1}번째 카드, ${cardName}입니다. 이 카드는 어떤 메시지를 전달할까요?`;
    }
    
          // 고민 기반 10카드 스프레드 (켈틱 크로스 확장 또는 생명의 나무)
    else if (isCustomTenCard) {
      const tenCardPrompts = [
        `첫 번째 카드, ${cardName}입니다.\n\n이 카드는 당신의 질문에 대한 **핵심 주제**와 **현재의 에너지**를 보여줍니다.\n\n모든 것의 중심에 있는 상황입니다.`, // 현재/핵심
        `두 번째 카드, ${cardName}입니다.\n\n이 카드는 당신의 상황을 **가로지르는 영향력**이나 **직면한 즉각적 도전**을 나타냅니다.\n\n어떤 요소가 상황을 복잡하게 만드는지 살펴보세요.`, // 교차 영향력
        `세 번째 카드, ${cardName}입니다.\n\n이 카드는 당신의 상황 **아래에 놓인 기반**과 **깊은 무의식적 영향**을 보여줍니다.\n\n보이지 않는 토대가 무엇인지 인식하세요.`, // 기반/무의식
        `네 번째 카드, ${cardName}입니다.\n\n이 카드는 당신의 **과거 경험**과 **이미 지나간 영향력**을 나타냅니다.\n\n어떤 지난 사건이 현재까지 영향을 미치고 있는지 고려해보세요.`, // 과거
        `다섯 번째 카드, ${cardName}입니다.\n\n이 카드는 당신이 앞으로 **달성할 수 있는 가능성**과 **잠재적 정점**을 보여줍니다.\n\n무엇이 최상의 결과가 될 수 있을까요?`, // 가능한 최상의 결과
        `여섯 번째 카드, ${cardName}입니다.\n\n이 카드는 당신의 **가까운 미래**와 **곧 전개될 에너지**를 암시합니다.\n\n어떤 상황이 곧 나타날지 준비하세요.`, // 가까운 미래
        `일곱 번째 카드, ${cardName}입니다.\n\n이 카드는 당신 **자신의 태도**와 **현재 에너지**가 상황에 미치는 영향을 보여줍니다.\n\n당신의 접근 방식이 어떤 역할을 하고 있나요?`, // 자신/태도
        `여덟 번째 카드, ${cardName}입니다.\n\n이 카드는 당신을 **둘러싼 환경**과 **외부적 영향**을 나타냅니다.\n\n주변 사람들과 상황이 어떻게 작용하고 있는지 확인하세요.`, // 외부 영향/환경
        `아홉 번째 카드, ${cardName}입니다.\n\n이 카드는 당신의 **희망과 두려움**을 반영하며, **내면의 감정**이 결과에 어떤 영향을 미치는지 보여줍니다.\n\n어떤 기대가 현실을 형성하고 있나요?`, // 희망과 두려움
        `열 번째 카드, ${cardName}입니다.\n\n이 카드는 이 여정의 **궁극적 결과**와 **장기적 해결책**을 암시합니다.\n\n모든 에너지가 어우러져 형성될 최종 결론을 보여줍니다.` // 최종 결과
      ];
      return tenCardPrompts[index] || `당신의 고민에 대한 ${index + 1}번째 카드, ${cardName}입니다. 이 카드는 어떤 메시지를 전달할까요?`;
    }
    
    // 기본 메시지 (다른 모든 경우)
    return `${index + 1}번째 카드, ${cardName}입니다.\n이 카드는 어떤 의미를 가지고 있을까요?`;
  };

  // 카드 이름 호버 시 모달 표시 이벤트 핸들러
  const handleCardNameClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const target = event.target as HTMLElement;
    if (target.classList.contains('card-name-interactive')) {
      const cardNameFromDataAttr = target.getAttribute('data-card-name');
      if (cardNameFromDataAttr) {
        // 전체 카드 목록에서 이름으로 카드 찾기 (대소문자, 공백 등 고려하여 정확도 높일 필요 있음)
        const foundCard = tarotCardsData.find(card => 
          card.name.trim().toLowerCase() === cardNameFromDataAttr.trim().toLowerCase()
        );
        if (foundCard) {
          setModalCardDetails(foundCard);
          setShowCardDetailModal(true);
        } else {
          console.warn(`Card not found in tarotCardsData: ${cardNameFromDataAttr}`);
          // setShowCardDetailModal(false); // 필요시 모달 숨김
        }
      }
    }
  };

  // 모달 닫기 핸들러
  const handleCloseModal = () => {
    setShowCardDetailModal(false);
    setModalCardDetails(null);
  };

  // 랜덤 카드 슬라이드 효과음 선택 함수
  const getRandomCardSlideSound = (): string => {
    const cardSlides = [
      SOUND_PATHS.cardSlide1,
      SOUND_PATHS.cardSlide2,
      SOUND_PATHS.cardSlide3,
      SOUND_PATHS.cardSlide4,
      SOUND_PATHS.cardSlide5,
      SOUND_PATHS.cardSlide6,
      SOUND_PATHS.cardSlide7,
      SOUND_PATHS.cardSlide8,
    ];
    const randomIndex = Math.floor(Math.random() * cardSlides.length);
    return cardSlides[randomIndex];
  };



  // Define renderConcernInputSection within the component for proper scope access
  const renderConcernInputSection = () => {
    // Removed handleSimpleConcernSubmit as handleConcernSubmit is now at component level
    const characterCount = userConcern.length;
    const isValidLength = characterCount >= 30 && characterCount <= 500;

    return (
      <div className="concern-input-section" style={{ animation: 'fadeIn 1s ease-out', paddingTop: '50px' }}> 
        <h3 className="section-title" style={{ fontFamily: "'Cute Font', 'Poor Story', cursive", fontSize: '2.8em', color: '#E6E6FA', marginBottom: '20px' }}>
          별에게 당신의 고민을 속삭여주세요
        </h3>
        <p className="section-subtitle" style={{ fontSize: '1.2em', color: '#C0C0C0', marginBottom: '35px' }}>
          구체적인 질문은 별들이 더 명확한 답을 찾는 데 도움을 줍니다.
        </p>
        <div style={{ position: 'relative', width: 'clamp(300px, 90%, 600px)', margin: '0 auto' }}>
        <textarea
          className="concern-textarea"
          placeholder="예: 저의 다음 직업적 결정은 무엇이어야 할까요?"
          value={userConcern}
          onChange={(e) => setUserConcern(e.target.value)}
          rows={5}
            maxLength={500}
          style={{
            // width and other specific styles are now in concern-textarea CSS class
            // minHeight: '120px', // from CSS
            // padding: '18px', // from CSS
            // fontSize: '1.1em', // from CSS
            // lineHeight: '1.6', // from CSS
            // marginBottom: '30px', // from CSS
            // resize: 'none', // from CSS
            // fontFamily: "'Nanum Gothic', 'Malgun Gothic', sans-serif" // from CSS
          }}
          disabled={isLoadingFortune}
        />
          <div style={{ 
            position: 'absolute', 
            bottom: '10px', 
            right: '15px', 
            fontSize: '0.85rem', 
            color: characterCount < 30 ? '#ff6b6b' : characterCount > 450 ? '#ffb86c' : '#a0a0a0',
            transition: 'color 0.3s ease'
          }}>
            {characterCount}/500
          </div>
        </div>
        <button
          className="submit-concern-btn" 
          onClick={handleConcernSubmit} // Use the main handleConcernSubmit
          disabled={isLoadingFortune || !isValidLength}
          // style={{
          //   padding: '18px 35px', // from CSS
          //   fontSize: '1.2em', // from CSS
          //   background: 'linear-gradient(145deg, #8A2BE2, #4B0082)', // from CSS
          //   boxShadow: '0 4px 10px rgba(138, 43, 226, 0.3), 0 1px 3px rgba(0,0,0,0.2) inset' // from CSS
          // }}
        >
          {isLoadingFortune ? (
            <div className="spinner" style={{ width: '20px', height: '20px', borderColor: 'white', borderLeftColor: 'transparent', margin: '0 auto' }}></div>
          ) : (
            characterCount < 30 ? '30자 이상 입력해주세요' : '질문 보내기'
          )}
        </button>
        {/* Back button to clear concern or go to a main menu if applicable */}
        <button 
            className="back-btn" 
            onClick={() => {
              setUserConcern(''); // Simple reset for now
              // If this page *only* does concern reading, there's no other type to select from here.
              // A button to go to a different *page* (e.g. main menu) would be needed if desired.
            }}
            disabled={isLoadingFortune}
            style={{ marginTop: '15px' }}
          >
            다시 작성하기
          </button>
      </div>
    );
  };

  // New function to handle selection of card count for custom tarot
  const handleCardCountSelect = async (count: number, type: FortuneType, cost: number, spreadId?: string) => {
    console.log(`Custom tarot selected: ${count} cards, type: ${type}, cost: ${cost} credits.`);
    
    if (!isLoggedIn || !token) {
      transitionToView('loginRequiredMessage');
      return;
    }

    // 할인 유효성 검증 (spreadId가 있는 경우)
    if (spreadId) {
      try {
        const discountValidationResponse = await fetch(`/api/tarot/spreads/${spreadId}/validate-discount`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ expectedCost: cost }),
        });
        
        const discountValidation = await discountValidationResponse.json();
        
        if (!discountValidation.success && discountValidation.priceChanged) {
          // 할인이 만료되었거나 가격이 변경된 경우
          alert(`할인 상태가 변경되었습니다. 현재 가격: ${discountValidation.currentCost}크레딧\n스프레드 선택 페이지로 돌아갑니다.`);
          // 스프레드 목록 새로고침 후 선택 페이지로 이동
          await loadActiveSpreads();
          setCurrentView('cardCountSelection');
          return;
        }
      } catch (error) {
        console.error('할인 유효성 검증 실패:', error);
        // 검증 실패 시에도 안전하게 스프레드 선택 페이지로 이동
        alert('할인 상태 확인 중 오류가 발생했습니다. 스프레드 선택 페이지로 돌아갑니다.');
        await loadActiveSpreads();
        setCurrentView('cardCountSelection');
        return;
      }
    }

    // Set 5-card spread type randomly if that's the choice
    if (type === 'customFiveCard') {
    } else {
    }

    // 선택된 스프레드 ID 저장
    setSelectedSpreadId(spreadId || null);
    
    // Simulate API call for pre-check
    setIsLoadingFortune(true);
    // This pre-check should ideally verify if the user can afford 'cost' for 'type'
    // For now, we directly use the cost passed.
    // In a real scenario, the backend would validate 'type' and return its actual cost if not passed,
    // or confirm the passed 'cost' is correct for 'type'.
    try {
        // Example: /api/fortune/pre-check?fortuneType=customFiveCard (or a generic custom type with count/cost)
        const response = await fetch(`/api/fortune/pre-check?fortuneType=${type}`, {
            headers: { 'Authorization': `Bearer ${token}` },
        });
        const preCheckData = await response.json();
        setIsLoadingFortune(false);

        if (!response.ok) {
            console.error(`[Critical Error] Custom Tarot (${type}) Pre-check HTTP 오류: ${response.status}`, preCheckData);
            setApiError(preCheckData.error || '운세 가능 여부 확인 중 오류가 발생했습니다.');
            addChatMessage(preCheckData.error || '운세 가능 여부 확인 중 오류가 발생했습니다. 다시 시도해주세요.', 'system');
            return;
        }

        if (!preCheckData.isAllowed) {
            if (preCheckData.reason === 'insufficient_credits') {
                if (updateUserCredits && typeof preCheckData.currentCredits === 'number') {
                    updateUserCredits(preCheckData.currentCredits);
                }
                // Ensure selectedFortuneType is set to the *specific* custom type for correct message rendering
                setSelectedFortuneType(type); 
                setCurrentFortuneCost(cost); // Set cost for the message
                transitionToView('creditsRequiredMessage');
            } else if (preCheckData.reason === 'login_required' || preCheckData.reason === 'login_required_for_paid_fortune') {
                transitionToView('loginRequiredMessage');
            } else { // Other reasons like cooldown (though custom tarot shouldn't have cooldown based on current spec)
                setSelectedFortuneType(type);
                setCooldownMessageDetail(preCheckData.error || '알 수 없는 이유로 타로점을 볼 수 없습니다.');
                transitionToView('cooldownMessage'); // Or a generic error view
            }
            return;
        }
        
        // Pre-check successful
        setSelectedFortuneType(type);
        setSelectedCardCountForCustom(count);
        setCurrentNumCardsToSelect(count);
        setCurrentFortuneCost(cost);
    
        // Prepare for card drawing
        const systemMessage = `이 질문에 대한 답을 찾기 위해, 마음이 이끄는 카드 ${count}장을 선택해주세요.`;
        addChatMessage(systemMessage, 'system');
        setCardsForSelection(getRandomCards(tarotCardsData, tarotCardsData.length));
        setSelectedCards([]);
        setShowCardSelectionButton(false); // Ensure this is reset
        transitionToView('cardDrawing');

    } catch (error) {
        setIsLoadingFortune(false);
        console.error(`Custom Tarot (${type}) Pre-check API Error:`, error);
        const errorMessage = '운세 가능 여부 확인 중 네트워크 오류가 발생했습니다. 잠시 후 다시 시도해주세요.';
        setApiError(errorMessage);
        addChatMessage(errorMessage, 'system');
    }
  };

    // Renders buttons for selecting spread layout for custom tarot
  const renderCardCountSelectionSection = () => {
    return (
      <div className="tarot-spread-selection scene-transition">
        <div className="spread-selection-header">
          <h2 className="spread-selection-title">운명의 실타래</h2>
          <p className="spread-selection-subtitle">어떤 패턴으로 당신의 고민을 풀어보시겠습니까?</p>
        </div>
        
        <div className="spread-grid-container">
          {isLoadingSpreads ? (
            // 로딩 중일 때는 스켈레톤 카드들 표시
            Array.from({ length: 6 }).map((_, index) => (
              <div key={`skeleton-${index}`} className="spread-grid-item">
                <div className="spread-card skeleton">
                  <div className="spread-card-inner">
                    <div className="spread-visual-container">
                      <div className="spread-visual spread-skeleton">
                        <div className="skeleton-dots">
                          {Array.from({ length: 3 }).map((_, i) => (
                            <div key={i} className="skeleton-dot"></div>
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="spread-title skeleton-text"></div>
                    <div className="spread-meta skeleton-text"></div>
                  </div>
                </div>
              </div>
            ))
          ) : apiSpreads.length === 0 ? (
            // 연결 실패시 안내 메시지
            <div className="connection-error">
              <div className="error-icon">⚡</div>
              <h3>연결 상태를 확인 중입니다...</h3>
              <p>잠시 후 다시 시도해주세요</p>
            <button
                className="retry-btn" 
                onClick={loadActiveSpreads}
              disabled={isLoadingFortune}
              >
                다시 시도
            </button>
            </div>
          ) : (
            // 정상적으로 데이터가 로드된 경우
            apiSpreads.map(spread => (
              <div 
                key={spread.id} 
                className="spread-grid-item"
                onClick={() => !isLoadingFortune && handleCardCountSelect(spread.cardCount, spread.spreadType as FortuneType, spread.finalCost || spread.cost, spread.id)}
              >
                <div className="spread-card">
                  <div className="spread-card-inner">
                    <div className="spread-visual-container">
                      <div className={`spread-visual ${spread.className}`}>
                        {(spread.positions && spread.positions.length > 0) ? (
                          // Manager에서 설정한 위치 사용
                          spread.positions.map((pos: any, i: number) => (
                            <div 
                              key={pos.id || i} 
                              className="spread-dot"
                              style={{
                                position: 'absolute',
                                left: pos.left,
                                top: pos.top,
                                transform: pos.transform || 'translate(-50%, -50%)',
                                animationDelay: `${i * 0.1}s`
                              }}
                            ></div>
                          ))
                        ) : (
                          // 기본 레이아웃 사용
                          Array.from({ length: Math.min(spread.cardCount, 22) }).map((_, i) => (
                            <div 
                              key={i} 
                              className="spread-dot"
                              style={{
                                animationDelay: `${i * 0.1}s`
                              }}
                            ></div>
                          ))
                        )}
                      </div>
                    </div>
                    <div className="spread-title">{spread.name}</div>
                    <div className="spread-meta">
                      {spread.cardCount}장 · 
                      {spread.isDiscountActive ? (
                        <>
                          <span style={{textDecoration: 'line-through', opacity: 0.7}}>{spread.cost}</span>
                          <span className="discounted-price"> {spread.finalCost}</span>
                          <span style={{color: '#ff6b6b', fontSize: '0.8em'}}> (-{spread.discount}%)</span>
                        </>
                      ) : (
                        `${spread.finalCost}`
                      )} 크레딧
                      {spread.discountTimeRemaining && spread.discountTimeRemaining !== '할인 종료' && (
                        <div className="discount-timer">
                          {spread.discountTimeRemaining}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
        
        <div className="spread-selection-footer">
        <button 
            className="spread-back-btn" 
            onClick={() => transitionToView('meditationIntro')}
            disabled={isLoadingFortune}
          >
            이전으로
        </button>
        </div>
      </div>
    );
  };

  // Add a useEffect to measure the container dimensions when it's rendered
  useEffect(() => {
    if (spreadContainerRef.current && currentView === 'cardRevealAndInterpretation') {
      const updateSpreadContainerDimensions = () => {
        if (spreadContainerRef.current) {
          // setSpreadContainerDimensions was removed as the state variable is no longer used.
        }
      };

      // Initial measurement
      updateSpreadContainerDimensions();

      // 뷰가 로드되면 애니메이션 시작
      setCardsInStartPosition(true);
      setTimeout(() => {
        startCardAnimationSequence();
      }, 1000);

      // Listen for window resize events
      window.addEventListener('resize', updateSpreadContainerDimensions);

      // Cleanup
      return () => {
        window.removeEventListener('resize', updateSpreadContainerDimensions);
      };
    }
  }, [currentView, spreadContainerRef.current]);

  // 카드 이동 애니메이션 시퀀스 시작 함수 수정
  const startCardAnimationSequence = () => {
    if (!cardsInStartPosition || cardsMovingToPosition) return; // 이미 진행 중이면 중복 실행 방지
    
    // 처음부터 카드가 부채꼴로 펼쳐진 상태로 설정
    setCardsInitializing(true);
    
    console.log("카드 애니메이션 시작: 초기 위치에 부채꼴로 배치...");
    
    // 사용자가 부채꼴 카드를 인식할 수 있는 충분한 시간 제공
    setTimeout(() => {
      // 부채꼴이 이미 완성되었으므로, 바로 다음 단계로 이동
      setFanAnimationComplete(true);
      
      console.log("카드 이동 애니메이션 준비 완료");
      // 사용자가 부채꼴 카드를 충분히 인식한 후 카드 이동 시작
      setTimeout(() => {
        console.log("카드 이동 시작");
        setCardsMovingToPosition(true);
        
        // 첫 카드부터 시작
        moveNextCard(0);
      }, 1000); // 카드 이동 시작 전 딜레이 증가 (500ms -> 1000ms)
    }, 1000); // 초기 대기 시간 증가 (600ms -> 1000ms)
  };
  
  // 순차적 카드 이동 함수 수정 - 이동 속도 2배 증가
  const moveNextCard = (index: number) => {
    if (index >= selectedCards.length) {
      // 모든 카드 이동 완료
      setCardsMovingToPosition(false);
      setCardsInStartPosition(false); // All cards are now in their spread positions
      return;
    }
    
    setCurrentMovingCardIndex(index);
    
    // 현재 처리할 카드의 Z-Index를 증가시켜 최상위로 올림 
    setCardZIndices(prevZIndices => {
      const newZIndices = [...prevZIndices];
      
      // Find the current maximum z-index among all cards
      let currentMaxZ = baseZIndex -1; // Initialize lower than baseZIndex
      for (let i = 0; i < newZIndices.length; i++) {
        if (newZIndices[i] !== undefined && newZIndices[i] > currentMaxZ) {
          currentMaxZ = newZIndices[i];
        }
      }
      
      // Set the z-index for the current card to be higher than any other card
      newZIndices[index] = currentMaxZ + 1; 
      
      return newZIndices;
    });
    
    // 이제 다음 카드로 즉시 넘어가지 않고, 현재 카드가 완전히 처리될 때까지 기다립니다.
    const isCrossingCard = selectedFortuneType === 'customTenCard' && index === 1;
    
    // 카드 이동 시작 전에 충분한 딜레이 추가
    setTimeout(() => {
      // 이 시점에서 부채꼴 상태에서 카드가 바로 이동할 수 있도록 초기화 상태 해제
      setCardsInitializing(false);
      
      // 카드가 실제로 움직이기 시작할 때까지 약간의 추가 딜레이
      setTimeout(() => {
        // 카드 이동 함수 - 스무스 애니메이션을 위한 CSS 트랜지션 적용
        // 카드 이동이 완료될 때까지 기다린 후 다음 단계 진행
        const movementDelay = 1800; // 카드 이동 애니메이션과 같은 시간
        
        // 켈틱 크로스 두번째 카드인 경우와 일반 카드 구분
        if (!isCrossingCard) {
          // 일반 카드는 위치 도달 후 약간의 딜레이 후 뒤집기
          setTimeout(() => {
            // 일반 카드 뒤집기
            setCardFlipStates(prev => {
              const newStates = [...prev];
              newStates[index] = true;
              return newStates;
            });
            
            // 카드 뒤집기 효과음 재생 (랜덤 선택)
            playSound(getRandomCardSlideSound());
            
            // 현재 공개/뒤집기 중인 카드 인덱스 설정
            setCurrentRevealingCard(index);
            // 하이라이트 효과 적용 (확대 및 아웃라인)
            setHighlightedCardIndex(index);
            
            // 카드 뒤집힌 후, 개별 카드 소개 시작
            setTimeout(() => {
              const cardToIntroduce = selectedCards[index];
              setCurrentCardIntroText(generateCardIntroductionText(cardToIntroduce, index));
              setIsIntroducingCard(true);
            }, 800); // 카드 뒤집기 애니메이션 시간 고려
          }, 1000); // 카드 이동 완료 후 딜레이 증가 (800ms -> 1000ms)
        } else {
          // 켈틱 크로스의 두번째 카드 특수 처리
          // 1. 일단 위치로 이동 (세로 상태로)
          // 2. 위치에 도달한 후, 가로로 전환 (애니메이션)
          // 3. 가로 전환 후, 카드 뒤집기
          
          // 위치에 도달 후 일정 시간 후에 가로 전환하는 클래스 추가
          setTimeout(() => {
            // 가로 방향으로 회전 클래스 추가
            setCelticCrossSecondCardRotating(true);
            
            // 가로 회전 효과음 추가
            playSound('cardSlide1');
            
            // 가로 회전 애니메이션이 완료된 후에 카드 뒤집기 시작
            setTimeout(() => {
              setCardFlipStates(prev => {
                const newStates = [...prev];
                newStates[index] = true;
                return newStates;
              });
              
              // 카드 뒤집기 효과음 재생
              playSound(getRandomCardSlideSound());
              
              // 현재 공개/뒤집기 중인 카드 인덱스 설정
              setCurrentRevealingCard(index);
              // 하이라이트 효과 적용 (확대 및 아웃라인)
              setHighlightedCardIndex(index);
              
              // 카드 뒤집힌 후, 개별 카드 소개 시작
              setTimeout(() => {
                const cardToIntroduce = selectedCards[index];
                setCurrentCardIntroText(generateCardIntroductionText(cardToIntroduce, index));
                setIsIntroducingCard(true);
              }, 800); // 카드 뒤집기 애니메이션 시간 고려
            }, 800); // 가로 전환 애니메이션 시간 고려
          }, 1000); // 카드 이동 완료 후 딜레이 증가 (800ms -> 1000ms)
        }
      }, 300); // 추가 딜레이로 부드러운 시작 구현
    }, 500); // 딜레이 증가 (100ms -> 500ms)
  };
  
  // 카드에 적용할 시작 위치 스타일 (중앙 하단에 일렬 배치)
  const getCardStartPosition = (index: number, totalCards: number): React.CSSProperties => {
    // 카드 크기 계산 (renderCardReveal에서와 동일한 로직)
    const cardW = window.innerWidth <= 576 ? 60 : window.innerWidth <= 768 ? 70 : 100;
    const cardH = cardW * 1.6;
    
    // 카드가 20% 정도만 보이도록 겹치는 간격 계산
    const cardOverlapOffset = cardW * 0.2; // 카드 너비의 20%만큼 간격
    const totalDeckWidth = (totalCards - 1) * cardOverlapOffset + cardW;
    
    // 중앙에서 시작하여 좌우로 배치
    const startX = -(totalDeckWidth / 2) + (cardW / 2);
    const xOffset = startX + (index * cardOverlapOffset);
    
    // 초기 상태 - 중앙 하단에 일렬로 배치
    if (cardsInitializing) {
      return {
        position: 'absolute',
        left: '50%',
        bottom: '-200px',
        transform: `translateX(calc(-50% + ${xOffset}px))`,
        transformOrigin: 'bottom center',
        zIndex: 1000 + index, // 나중 카드가 위에 오도록
        width: `${cardW}px`,
        height: `${cardH}px`,
        transition: 'none' // 초기에는 트랜지션 없음
      };
    }
    
    // 애니메이션 상태 (카드가 이동할 때)
    return {
      position: 'absolute',
      left: '50%',
      bottom: '-200px',
      transform: `translateX(calc(-50% + ${xOffset}px))`,
      transformOrigin: 'bottom center',
      zIndex: 1000 + index,
      width: `${cardW}px`,
      height: `${cardH}px`,
      transition: 'all 1.5s cubic-bezier(0.25, 0.1, 0.25, 1)' // 부드러운 이징으로 변경
    };
  };

  // 리셋 함수 내에서 highlightedCardIndex도 초기화
  const resetTarotReadingState = () => {
    // 1. 카드 관련 상태 초기화
    setSelectedCards([]);
    setCardFlipStates([]);
    // setCurrentCardIndex(0); // 이 부분 주석 처리 또는 수정 필요 - 린터 에러
    setCurrentRevealingCard(null);
    setHighlightedCardIndex(null); // 하이라이트 카드 인덱스 초기화
    setCardsInStartPosition(true);
    setCardsMovingToPosition(false);
    setCurrentMovingCardIndex(null);
    setIsIntroducingCard(false);
    setCurrentCardIntroText('');
    setAutoRevealActive(false);
    setUserConcern(''); // Reset user concern
    setCelticCrossSecondCardRotating(false);
    setCardZIndices([]); // Z-Index 상태 초기화
    setCardsInitializing(true); // 카드 초기화 상태 초기화
    setFanAnimationComplete(false); // 부채꼴 애니메이션 완료 상태 초기화
    setFanHorizontalOffset(0); // Fan offset 초기화

    // 4. 짧은 지연 후 페이드 아웃 및 오버레이 표시 (뷰 변경이 반영될 시간)
    setTimeout(() => {
      // setShowFadeOverlay(true); // 이 부분도 린터 에러 - 존재하지 않는 변수
      setShowTransitionOverlay(true); // 올바른 변수로 대체
      
      // 5. 오버레이 페이드인이 완료된 후 뷰 변경
      setTimeout(() => {
        setCurrentView('concernInput'); // 'selectFortune'은 존재하지 않는 View 타입 - 린터 에러 수정
        
        // 6. 뷰 변경 후 오버레이 숨기기
        setTimeout(() => {
          // setShowFadeOverlay(false); // 이 부분도 린터 에러 - 존재하지 않는 변수
          setShowTransitionOverlay(false); // 올바른 변수로 대체
        }, 800);
      }, 400);
    }, 800);
  };

  return (
    <>
      {/* CSS 스타일 추가 */}
      <style>
        {sceneTransitionStyles}
      </style>
      
      {/* 영구 배경 오버레이 - 전체화면 운세 뷰에서만 표시 */}
      {isFullScreenView && (
        <div className="background-overlay">
          {/* Add these divs for the mist effect */}
          <div className="mist-layer mist-layer-1"></div>
          <div className="mist-layer mist-layer-2"></div>
          <div className="mist-layer mist-layer-3"></div>
        </div>
      )}
      
      {/* 장면 전환 오버레이 - 트랜지션 중에 보이는 검은 화면 */}
      <div 
        className={`transition-overlay ${showTransitionOverlay ? 'active' : ''}`}
      >
        {/* 테마에 맞는 로딩 스피너 (예: 별 모양) */}
        <svg 
          className="themed-spinner"
          viewBox="0 0 24 24" 
          xmlns="http://www.w3.org/2000/svg"
          style={{
            width: '40px', 
            height: '40px', 
            animation: 'pulseAndSpin 2s linear infinite',
          }}
        >
          <path 
            d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2L9.19 8.63L2 9.24l5.46 4.73L5.82 21L12 17.27z"
            fill="#ffeb0b" 
          />
        </svg>
      </div>
      
      {/* 컨텐츠 래퍼 - 불투명도 애니메이션 적용 */}
      <div className="fortune-content" style={{ 
        opacity: contentOpacity, 
        transition: 'opacity 0.8s ease-in-out',
        position: 'relative', // Ensure independent stacking context
        zIndex: isFullScreenView ? 9995 : 'auto' // Only set high z-index when in fullscreen mode
      }}>
        {/* 기존 컴포넌트들 */}
        {renderSoundControls()}
        {/* 오디오 요소들은 공통 훅에서 관리됨 */}

        {/* 우주 진입 애니메이션 */}
        {isEnteringSpace && <div className="space-entry-animation"></div>}

        {/* 메인 컨텐츠 */}
        {!isEnteringSpace && isFullScreenView ? (
          <div 
            className={`fortune-fullscreen-view view-${currentView}`}
            style={{
              position: 'fixed', 
              top: 0, 
              left: 0, 
              width: '100vw', 
              height: '100vh', 
              background: 'transparent', // 검은색 배경은 background-overlay가 담당
              color: 'white', 
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: currentView === 'finalInterpretation' ? 'flex-start' : 'center',
              textAlign: 'center',
              padding: currentView === 'finalInterpretation' ? '10px 15px' : '1vh 15px',
              paddingTop: currentView === 'finalInterpretation' ? '20px' : '1vh',
              zIndex: 9995,
              overflowY: 'auto',
            }}
          >
            {/* 뒤로 가기 버튼 추가 */}
            <div
              className="fortune-back-button"
              onClick={handleNavigateToHome}
              style={{
                position: 'absolute',
                top: '20px',
                left: '20px',
                fontSize: '24px',
                cursor: 'pointer',
                zIndex: 9999,
                width: '40px',
                height: '40px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '50%',
                background: 'rgba(0, 0, 0, 0.4)',
                boxShadow: '0 0 10px rgba(0, 0, 0, 0.3)',
                transition: 'all 0.3s ease',
              }}
            >
              ←
            </div>
            {renderContent()} 
          </div>
        ) : !isEnteringSpace ? (
          <div className="fortune-page" style={{ position: 'relative', zIndex: 'auto' }}>
      <div className="fortune-container" style={{
        width: '100%',
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '40px 20px',
        background: 'linear-gradient(135deg, rgba(20, 10, 40, 0.9) 0%, rgba(40, 20, 80, 0.85) 100%)',
        backgroundSize: '200% 200%',
        animation: 'mysticalShine 15s ease infinite',
        backdropFilter: 'blur(10px)',
        borderRadius: '0',
        boxShadow: 'inset 0 0 100px rgba(80, 40, 160, 0.3), 0 0 50px rgba(0, 0, 0, 0.5)',
        overflow: 'hidden',
        position: 'relative',
      }}>
        {/* Cosmic background elements */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          overflow: 'hidden',
          zIndex: 0,
          opacity: 0.6
        }}>
          {/* Animated cosmic cloud background */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: 'url("/images/cosmic-clouds.webp")',
            backgroundSize: 'cover',
            opacity: 0.15,
            animation: 'mysticalShine 40s linear infinite alternate',
            filter: 'blur(8px)',
          }} />
        
          {/* Floating stars effect */}
          {Array.from({ length: 30 }).map((_, i) => {
            const size = Math.random() * 4 + 1;
            const top = Math.random() * 100;
            const left = Math.random() * 100;
            const animationDuration = Math.random() * 10 + 10;
            return (
              <div key={i} style={{
                position: 'absolute',
                width: `${size}px`,
                height: `${size}px`,
                borderRadius: '50%',
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                boxShadow: '0 0 10px rgba(255, 255, 255, 0.7), 0 0 20px rgba(255, 255, 255, 0.5)',
                top: `${top}%`,
                left: `${left}%`,
                animation: `celestialPulse ${animationDuration}s infinite ease-in-out ${Math.random() * 5}s`
              }} />
            );
          })}
          
          {/* Mystical symbols background - subtle symbols in background */}
          {Array.from({ length: 8 }).map((_, i) => {
            const symbols = ['✧', '⚝', '⚹', '⚶', '◯', '⚙', '⚘', '✺'];
            const size = Math.random() * 40 + 20; // Size between 20px and 60px
            const top = Math.random() * 90 + 5; // Keep away from extreme edges
            const left = Math.random() * 90 + 5;
            const rotation = Math.random() * 360; // Random rotation
            const opacity = Math.random() * 0.15 + 0.05; // Very subtle
            return (
              <div key={`symbol-${i}`} style={{
                position: 'absolute',
                fontSize: `${size}px`,
                top: `${top}%`,
                left: `${left}%`,
                color: 'rgba(255, 255, 255, 0.7)',
                opacity: opacity,
                transform: `rotate(${rotation}deg)`,
                textShadow: '0 0 15px rgba(255, 255, 255, 0.3)',
                pointerEvents: 'none'
              }}>
                {symbols[i % symbols.length]}
              </div>
            );
          })}
          
          {/* Cosmic nebula effects */}
          <div style={{
            position: 'absolute',
            top: '10%',
            right: '5%',
            width: '300px',
            height: '300px',
            background: 'radial-gradient(circle, rgba(120, 80, 200, 0.3) 0%, rgba(70, 40, 120, 0.1) 50%, transparent 70%)',
            borderRadius: '50%',
            filter: 'blur(30px)',
            animation: 'celestialPulse 20s infinite ease-in-out'
          }} />
          
          <div style={{
            position: 'absolute',
            bottom: '15%',
            left: '8%',
            width: '250px',
            height: '250px',
            background: 'radial-gradient(circle, rgba(80, 140, 200, 0.25) 0%, rgba(40, 70, 120, 0.1) 50%, transparent 70%)',
            borderRadius: '50%',
            filter: 'blur(25px)',
            animation: 'celestialPulse 24s infinite ease-in-out 3s'
          }} />
          
          {/* Purple/pink nebula */}
          <div style={{
            position: 'absolute',
            bottom: '40%',
            right: '20%',
            width: '350px',
            height: '350px',
            background: 'radial-gradient(ellipse, rgba(200, 100, 250, 0.15) 0%, rgba(150, 50, 200, 0.07) 40%, transparent 70%)',
            borderRadius: '50%',
            filter: 'blur(40px)',
            transform: 'rotate(45deg) scale(1.2, 0.8)',
            animation: 'celestialPulse 22s infinite ease-in-out 1s'
          }} />
          
          {/* Gold/warm nebula */}
          <div style={{
            position: 'absolute',
            top: '30%',
            left: '15%',
            width: '200px',
            height: '200px',
            background: 'radial-gradient(circle, rgba(255, 215, 115, 0.15) 0%, rgba(255, 180, 50, 0.05) 50%, transparent 70%)',
            borderRadius: '50%',
            filter: 'blur(20px)',
            animation: 'celestialPulse 18s infinite ease-in-out 2s'
          }} />
        </div>
        
        {/* Decorative border elements */}
        <div style={{
          position: 'absolute',
          top: '0',
          left: '0',
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          zIndex: 1
        }}>
          {/* Zodiac constellation pattern - top */}
          <div style={{
            position: 'absolute',
            top: '10px',
            left: '50%',
            transform: 'translateX(-50%)',
            width: '80%',
            maxWidth: '800px',
            height: '40px',
            background: 'url("/images/constellation-pattern.png")',
            backgroundSize: 'contain',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
            opacity: 0.15,
          }} />
          
          {/* Zodiac constellation pattern - bottom */}
          <div style={{
            position: 'absolute',
            bottom: '10px',
            left: '50%',
            transform: 'translateX(-50%) rotate(180deg)',
            width: '80%',
            maxWidth: '800px',
            height: '40px',
            background: 'url("/images/constellation-pattern.png")',
            backgroundSize: 'contain',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
            opacity: 0.15,
          }} />
        
          {/* Top left corner ornament */}
          <div style={{
            position: 'absolute',
            top: '20px',
            left: '20px',
            width: '80px',
            height: '80px',
            borderTop: '2px solid rgba(255, 215, 115, 0.3)',
            borderLeft: '2px solid rgba(255, 215, 115, 0.3)',
            borderTopLeftRadius: '15px'
          }} />
          
          {/* Bottom right corner ornament */}
          <div style={{
            position: 'absolute',
            bottom: '20px',
            right: '20px',
            width: '80px',
            height: '80px',
            borderBottom: '2px solid rgba(255, 215, 115, 0.3)',
            borderRight: '2px solid rgba(255, 215, 115, 0.3)',
            borderBottomRightRadius: '15px'
          }} />
          
          {/* Top right corner ornament */}
          <div style={{
            position: 'absolute',
            top: '20px',
            right: '20px',
            width: '80px',
            height: '80px',
            borderTop: '2px solid rgba(255, 215, 115, 0.3)',
            borderRight: '2px solid rgba(255, 215, 115, 0.3)',
            borderTopRightRadius: '15px'
          }} />
          
          {/* Bottom left corner ornament */}
          <div style={{
            position: 'absolute',
            bottom: '20px',
            left: '20px',
            width: '80px',
            height: '80px',
            borderBottom: '2px solid rgba(255, 215, 115, 0.3)',
            borderLeft: '2px solid rgba(255, 215, 115, 0.3)',
            borderBottomLeftRadius: '15px'
          }} />
        </div>
        
        {/* Content container with improved styling */}
        <div style={{
          position: 'relative',
          zIndex: 2,
          width: '100%',
          maxWidth: 'min(1200px, 92%)', // Responsive width
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          padding: window.innerWidth < 768 ? '20px 15px' : '30px', // Responsive padding
          background: 'rgba(30, 15, 60, 0.4)',
          backdropFilter: 'blur(4px)',
          borderRadius: '20px',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
          margin: '20px 0',
          transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        }}>
          {/* Decorative moon phase icon at the top */}
          <div style={{
            position: 'absolute',
            top: '-25px',
            left: '50%',
            transform: 'translateX(-50%)',
            width: '50px',
            height: '50px',
            background: 'rgba(30, 15, 60, 0.7)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 4px 15px rgba(0, 0, 0, 0.3)',
            pointerEvents: 'none' // Ensure it doesn't interfere with clicking
          }}>
            <span role="img" aria-label="moon phase" style={{ fontSize: '24px' }}>
              🌙
            </span>
          </div>
          
          {renderContent()}
        </div>
      </div>
    </div>
        ) : null }
      </div>

      {/* 카드 상세 정보 모달 */}
      {showCardDetailModal && modalCardDetails && (
        <div 
          className={`card-detail-modal-overlay ${showCardDetailModal ? 'active' : ''}`}
          onClick={handleCloseModal} // 오버레이 클릭 시 닫기
        >
          <div 
            className="card-detail-modal-content"
            onClick={(e) => e.stopPropagation()} // 모달 내부 클릭 시 오버레이 닫힘 방지
          >
            <button className="card-detail-modal-close-btn" onClick={handleCloseModal}>&times;</button>
            <img 
              src={`/images/tarot/${modalCardDetails.imageName}`}
              alt={modalCardDetails.name} 
              className="card-detail-modal-image"
            />
            <div className="card-detail-modal-info">
              <h4>{modalCardDetails.name}</h4>
              <p>{modalCardDetails.description}</p>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default TarotReadingPage; 
