import { TarotCard, FortuneType, FiveCardSpreadType, ManagerSpreadPosition } from '../types/tarotTypes';

export const getRandomCards = (allCards: TarotCard[], count: number): TarotCard[] => {
  const shuffled = [...allCards].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

// Manager에서 설정한 스프레드 위치 데이터를 가져오는 함수
export const getManagerSpreadPositions = async (spreadType: string): Promise<ManagerSpreadPosition[]> => {
  try {
    const response = await fetch('/api/tarot/spreads/active');
    const data = await response.json();
    
    if (data.success) {
      const spread = data.spreads.find((s: any) => s.spreadType === spreadType);
      if (spread && spread.positions) {
        return Array.isArray(spread.positions) ? spread.positions : JSON.parse(spread.positions || '[]');
      }
    }
  } catch (error) {
    console.error('Manager 스프레드 위치 로드 실패:', error);
  }
  
  return [];
};

export const getSpreadLayoutStyles = ( 
  fortuneType: FortuneType, 
  index: number,
  totalCards: number,
  fiveCardSpreadType?: FiveCardSpreadType,
  cardContainerWidth?: number, 
  cardContainerHeight?: number,
  managerPositions?: ManagerSpreadPosition[]
): React.CSSProperties => {
  const basePosition: React.CSSProperties = {
    position: 'absolute',
    transition: 'all 0.5s ease-in-out',
  };
  
  // Manager에서 설정한 위치 데이터가 있으면 우선 사용
  if (managerPositions && managerPositions.length > index) {
    const managerPos = managerPositions[index];
    const containerWidth = cardContainerWidth || 800;
    const containerHeight = cardContainerHeight || 500;
    
    return {
      ...basePosition,
      left: managerPos.left,
      top: managerPos.top,
      transform: managerPos.transform || 'translate(-50%, -50%)',
    };
  }

  // Default card dimensions for calculations
  const cardW = window.innerWidth <= 576 ? 60 : window.innerWidth <= 768 ? 70 : 100; // px - responsive card size
  const cardH = cardW * 1.6; // Maintain aspect ratio
  const gap = cardW * 0.5; // Proportional gap

  // Use container dimensions or default to reasonable values
  const containerWidth = cardContainerWidth || 800;
  const containerHeight = cardContainerHeight || 500;
  
  // Check for mobile layouts
  const isMobile = window.innerWidth <= 768;
  const isVerySmall = window.innerWidth <= 576;

  // 켈틱 크로스 스프레드 특별 크기 조정
  let finalCardW = cardW;
  let finalCardH = cardH;
  
  // 10장 켈틱 크로스에서는 카드 크기를 약간 줄임
  if (fortuneType === 'customTenCard') {
    finalCardW = cardW * 0.85; // 카드 너비 85%로 축소
    finalCardH = cardH * 0.85; // 카드 높이도 같은 비율로 축소
  } else {
    finalCardW = cardW;
    finalCardH = cardH;
  }

  if (fortuneType === 'customThreeCard') {
    // 3-Card Linear Layout - works fine on all screens
    const totalWidth = totalCards * cardW + (totalCards - 1) * gap;
    const startLeft = (containerWidth - totalWidth) / 2;
    return {
      ...basePosition,
      top: '50%',
      left: `${startLeft + index * (cardW + gap)}px`,
      transform: 'translateY(-50%)',
    };
  } else if (fortuneType === 'customFourCard') {
    // 4-Card Cross Layout
    const crossPositions = [
      { top: `${containerHeight / 2 - cardH / 2}px`, left: `${containerWidth / 2 - cardW / 2}px` },             // Center
      { top: `${containerHeight / 2 - cardH * 1.5 - gap}px`, left: `${containerWidth / 2 - cardW / 2}px` },     // Top
      { top: `${containerHeight / 2 - cardH / 2}px`, left: `${containerWidth / 2 - cardW * 1.5 - gap}px` },     // Left
      { top: `${containerHeight / 2 - cardH / 2}px`, left: `${containerWidth / 2 + cardW * 0.5 + gap}px` },     // Right
    ];
    return { ...basePosition, ...crossPositions[index] };
  } else if (fortuneType === 'customFiveCard') {
    if (fiveCardSpreadType === 'W') {
      // W Shape with responsive adjustments
      if (isMobile) {
        // Simplified W for mobile (more vertically stacked)
        const mobileWPositions = [
          { top: '10%', left: `${containerWidth * 0.25 - cardW / 2}px` },  // Top-Left
          { top: '30%', left: `${containerWidth * 0.25 - cardW / 2}px` },  // Mid-Left
          { top: '50%', left: `${containerWidth * 0.5 - cardW / 2}px` },   // Center
          { top: '30%', left: `${containerWidth * 0.75 - cardW / 2}px` },  // Mid-Right
          { top: '10%', left: `${containerWidth * 0.75 - cardW / 2}px` },  // Top-Right
        ];
        return { ...basePosition, ...mobileWPositions[index] };
      }
      
      // Desktop W layout
      const wPositions = [
        { top: '15%', left: `${containerWidth * 0.1667 - cardW / 2}px` }, // Card 1 (Top-Left)
        { top: '45%', left: `${containerWidth * 0.3333 - cardW / 2}px` }, // Card 2 (Mid-Left)
        { top: '15%', left: `${containerWidth * 0.5 - cardW / 2}px` },    // Card 3 (Top-Center)
        { top: '45%', left: `${containerWidth * 0.6667 - cardW / 2}px` }, // Card 4 (Mid-Right)
        { top: '15%', left: `${containerWidth * 0.8333 - cardW / 2}px` }, // Card 5 (Top-Right)
      ];
      return { ...basePosition, ...wPositions[index] };
    } else { // Cross Shape
      // Cross is pretty responsive without major changes
      const crossPositions = [
        { top: `${containerHeight / 2 - cardH / 2}px`, left: `${containerWidth / 2 - cardW / 2}px` },             // Center
        { top: `${containerHeight / 2 - cardH * 1.5 - gap}px`, left: `${containerWidth / 2 - cardW / 2}px` },     // Top
        { top: `${containerHeight / 2 - cardH / 2}px`, left: `${containerWidth / 2 - cardW * 1.5 - gap}px` },     // Left
        { top: `${containerHeight / 2 - cardH / 2}px`, left: `${containerWidth / 2 + cardW * 0.5 + gap}px` },     // Right
        { top: `${containerHeight / 2 + cardH * 0.5 + gap}px`, left: `${containerWidth / 2 - cardW / 2}px` },     // Bottom
      ];
      return { ...basePosition, ...crossPositions[index] };
    }
  } else if (fortuneType === 'customSevenCard' || fortuneType === 'customHorseshoe') { // Horseshoe
    if (isMobile) {
      // More compact horseshoe for mobile - flipped vertically (apex at bottom)
      const mobileHorseshoePositions = [
        { top: `${containerHeight * 0.2}px`, left: `${containerWidth * 0.2 - cardW / 2}px` },   // Top-Left
        { top: `${containerHeight * 0.4}px`, left: `${containerWidth * 0.3 - cardW / 2}px` },   // Mid-Left
        { top: `${containerHeight * 0.6}px`, left: `${containerWidth * 0.4 - cardW / 2}px` },   // Bottom-Left
        { top: `${containerHeight * 0.8}px`, left: `${containerWidth * 0.5 - cardW / 2}px` },   // Bottom-Center (Apex)
        { top: `${containerHeight * 0.6}px`, left: `${containerWidth * 0.6 - cardW / 2}px` },   // Bottom-Right
        { top: `${containerHeight * 0.4}px`, left: `${containerWidth * 0.7 - cardW / 2}px` },   // Mid-Right
        { top: `${containerHeight * 0.2}px`, left: `${containerWidth * 0.8 - cardW / 2}px` },   // Top-Right
      ];
      return { ...basePosition, ...mobileHorseshoePositions[index] };
    }

    // Desktop horseshoe layout - flipped vertically (apex at bottom)
    const horseshoePositions = [
      { top: `${containerHeight * 0.15}px`, left: `${containerWidth * 0.125 - cardW / 2}px` },  // Top-Left
      { top: `${containerHeight * 0.35}px`, left: `${containerWidth * 0.25 - cardW / 2}px` },   // Mid-Left
      { top: `${containerHeight * 0.55}px`, left: `${containerWidth * 0.375 - cardW / 2}px` },  // Bottom-Left
      { top: `${containerHeight * 0.70}px`, left: `${containerWidth * 0.5 - cardW / 2}px` },    // Bottom-Center (Apex)
      { top: `${containerHeight * 0.55}px`, left: `${containerWidth * 0.625 - cardW / 2}px` },  // Bottom-Right
      { top: `${containerHeight * 0.35}px`, left: `${containerWidth * 0.75 - cardW / 2}px` },   // Mid-Right
      { top: `${containerHeight * 0.15}px`, left: `${containerWidth * 0.875 - cardW / 2}px` },  // Top-Right
    ];
    return { ...basePosition, ...horseshoePositions[index] };
  } else if (fortuneType === 'customTenCard' || fortuneType === 'customCelticCross') { // Celtic Cross
    // 켈틱 크로스 특별 조정 - 카드 크기를 약간 줄임
    const celticCardScale = 0.85;
    const cCardW = cardW * celticCardScale;
    const cCardH = cardH * celticCardScale;

    if (isMobile) {
      // More vertical Celtic Cross layout for mobile
      const mobileCCPositions = [
        // Cross Section (moved more to the left)
        { top: `${containerHeight * 0.35 - cCardH / 2}px`, left: `${containerWidth * 0.25 - cCardW / 2}px` },                  // 1. Present
        { top: `${containerHeight * 0.35 - cCardH / 2}px`, left: `${containerWidth * 0.25 - cCardW / 2}px`, transform: 'rotate(90deg)' }, // 2. Challenge
        { top: `${containerHeight * 0.35 - cCardH * 1.2 - gap}px`, left: `${containerWidth * 0.25 - cCardW / 2}px` },           // 3. Above
        { top: `${containerHeight * 0.35 + cCardH * 0.2 + gap}px`, left: `${containerWidth * 0.25 - cCardW / 2}px` },           // 4. Below
        { top: `${containerHeight * 0.35 - cCardH / 2}px`, left: `${containerWidth * 0.25 - cCardW * 1.2 - gap}px` },           // 5. Behind
        { top: `${containerHeight * 0.35 - cCardH / 2}px`, left: `${containerWidth * 0.25 + cCardW * 0.2 + gap}px` },          // 6. Ahead

        // Staff (vertical instead of on the right) - 위로 이동, 간격 조정
        { top: `${containerHeight * 0.65}px`, left: `${containerWidth * 0.75 - cCardW / 2}px` },                               // 7. Self (Bottom)
        { top: `${containerHeight * 0.45}px`, left: `${containerWidth * 0.75 - cCardW / 2}px` },                               // 8. Environment
        { top: `${containerHeight * 0.25}px`, left: `${containerWidth * 0.75 - cCardW / 2}px` },                               // 9. Hopes/Fears
        { top: `${containerHeight * 0.05}px`, left: `${containerWidth * 0.75 - cCardW / 2}px` },                               // 10. Outcome
      ];

      if (isVerySmall) {
        // Further compression for very small screens
        return {
          ...basePosition,
          ...mobileCCPositions[index],
          transform: index === 1 ? 'rotate(90deg) scale(0.8)' : 'scale(0.8)', // Scale down all cards
          width: `${cCardW * 0.8}px`,
          height: `${cCardH * 0.8}px`
        };
      }

      return {
        ...basePosition,
        ...mobileCCPositions[index],
        width: `${cCardW}px`,
        height: `${cCardH}px`
      };
    }

    // Desktop Celtic Cross layout
    const ccPositions = [
      // Cross Section
      { top: `${containerHeight * 0.45 - cCardH / 2}px`, left: `${containerWidth * 0.25 - cCardW / 2}px` },                 // 1. Present
      { top: `${containerHeight * 0.45 - cCardH / 2}px`, left: `${containerWidth * 0.25 - cCardW / 2}px`, transform: 'rotate(90deg)' }, // 2. Challenge
      { top: `${containerHeight * 0.45 - cCardH * 1.2 - gap}px`, left: `${containerWidth * 0.25 - cCardW / 2}px` },          // 3. Above
      { top: `${containerHeight * 0.45 + cCardH * 0.2 + gap}px`, left: `${containerWidth * 0.25 - cCardW / 2}px` },          // 4. Below
      { top: `${containerHeight * 0.45 - cCardH / 2}px`, left: `${containerWidth * 0.25 - cCardW * 1.2 - gap}px` },          // 5. Behind
      { top: `${containerHeight * 0.45 - cCardH / 2}px`, left: `${containerWidth * 0.25 + cCardW * 0.2 + gap}px` },         // 6. Ahead
      // Staff (right side) - 위로 이동, 간격 조정
      { top: `${containerHeight * 0.65}px`, left: `${containerWidth * 0.75 - cCardW / 2}px` },                // 7. Self (Bottom)
      { top: `${containerHeight * 0.45}px`, left: `${containerWidth * 0.75 - cCardW / 2}px` },                // 8. Environment
      { top: `${containerHeight * 0.25}px`, left: `${containerWidth * 0.75 - cCardW / 2}px` },                // 9. Hopes/Fears
      { top: `${containerHeight * 0.05}px`, left: `${containerWidth * 0.75 - cCardW / 2}px` },                // 10. Outcome (Top)
    ];

    if (index === 1) { // Crossing card specific style
      return {
        ...basePosition,
        ...ccPositions[index],
        zIndex: 1001,
        width: `${cCardW}px`,
        height: `${cCardH}px`
      };
    }
    return {
      ...basePosition,
      ...ccPositions[index],
      zIndex: 1000 - index,
      width: `${cCardW}px`,
      height: `${cCardH}px`
    }; // Ensure proper stacking order
  }

  // Default linear layout for 'today', 'year', or any other unspecified type
  const totalWidth = totalCards * cardW + (totalCards - 1) * gap;
  const startLeft = (containerWidth - totalWidth) / 2;
  return {
    ...basePosition,
    top: '50%', // Vertically centered
    left: `${startLeft + index * (cardW + gap)}px`,
    transform: 'translateY(-50%)',
  };
};
